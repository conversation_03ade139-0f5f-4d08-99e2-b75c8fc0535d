<?php

/**
 * @version    CVS: 1.0.0
 * @package    Com_Myaccount
 * <AUTHOR> lo <<EMAIL>>
 * @copyright  2019 kevin lo
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
defined('_JEXEC') or die;

JLoader::register('MyaccountHelper', JPATH_ADMINISTRATOR . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'com_myaccount' . DIRECTORY_SEPARATOR . 'helpers' . DIRECTORY_SEPARATOR . 'myaccount.php');

use \Joomla\CMS\Factory;
use \Joomla\CMS\MVC\Model\BaseDatabaseModel;

require_once(JPATH_SITE . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'com_top' . DIRECTORY_SEPARATOR . 'helpers' . DIRECTORY_SEPARATOR . 'toplog.php');

/**
 * Class MyaccountFrontendHelper
 *
 * @since  1.6
 */
class MyaccountHelpersMyaccount
{
	public static $ai;
	public static $temp;
	public static $elec;
	public static $elec_cic;
	public static $elec_acuvim;
	public static $elec_tatung;
	public static $elec_pem333;
	public static $elec_pem575;
	public static $elec_shihlin; 
	public static $elec_cicbaw1a2a; 
	public static $elec_opcda; 
	public static $elec_general_opcda; 
	public static $elec_aem_drb; 
	public static $elec_weema_1p; 
	public static $elec_weema_3p; 
	public static $elec_solar_primevolt;
	public static $elec_solar_shihlinspm8;
	public static $elec_solar_general;
	public static $elec_vmr_mp7;
	public static $water_meter;
	public static $water_meter_tkd;
	public static $water_meter_general;
	public static $apage;
	public static $adpage;
	/**
	 * Get an instance of the named model
	 *
	 * @param   string  $name  Model name
	 *
	 * @return null|object
	 */
	public static function getModel($name)
	{
		$model = null;

		// If the file exists, let's
		if (file_exists(JPATH_SITE . '/components/com_myaccount/models/' . strtolower($name) . '.php'))
		{
			require_once JPATH_SITE . '/components/com_myaccount/models/' . strtolower($name) . '.php';
			$model = BaseDatabaseModel::getInstance($name, 'MyaccountModel');
		}

		return $model;
	}

	/**
	 * Gets the files attached to an item
	 *
	 * @param   int     $pk     The item's id
	 *
	 * @param   string  $table  The table's name
	 *
	 * @param   string  $field  The field's name
	 *
	 * @return  array  The files
	 */
	public static function getFiles($pk, $table, $field)
	{
		$db = Factory::getDbo();
		$query = $db->getQuery(true);

		$query
			->select($field)
			->from($table)
			->where('id = ' . (int) $pk);

		$db->setQuery($query);

		return explode(',', $db->loadResult());
	}

    /**
     * Gets the edit permission for an user
     *
     * @param   mixed  $item  The item
     *
     * @return  bool
     */
    public static function canUserEdit($item)
    {
        $permission = false;
        $user       = Factory::getUser();

        if ($user->authorise('core.edit', 'com_myaccount'))
        {
            $permission = true;
        }
        else
        {
            if (isset($item->created_by))
            {
                if ($user->authorise('core.edit.own', 'com_myaccount') && $item->created_by == $user->id)
                {
                    $permission = true;
                }
            }
            else
            {
                $permission = true;
            }
        }

        return $permission;
    }

		public static function sendMessage($item)
	  {
			//socket_create
			$socket = socket_create(AF_UNIX, SOCK_STREAM, 0);

			if ($socket < 0) {
			    echo "---Failed: socket_create() failed! Reason: " . socket_strerror($socket) . "<br>";
					return;
			}

			//socket_connect
			$result = socket_connect($socket, "/tmp/phpsocket");

			if ($result < 0) {
			    echo "---Failed: socket_connect() failed! Reason: " . socket_strerror($result) . "<br>";

	        //Close
	        socket_close($socket);
					return;
			}

			$in = 'ipcam='.$item->ipcam.'&caller='.$item->caller.'&callee='.$item->callee.'&msg='.$item->message.'&id='.$item->id;//input, transfer msg to server side

			//socket_write
			if(!socket_write($socket, $in, strlen($in))) {
			    echo "---Failed: socket_write() failed! Reason: " . socket_strerror($socket) . "\n";
			}


			//Close
			socket_close($socket);


	  }
		public static function getTemps()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.*');

			$query->from('`#__device_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.dio_id > 0' );
			//$query->where('a.type = '.self::$ai );
			$query->where('a.dio_type in (4,11,28,38, 39)' );

			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);

			 $items = (array) $db->loadObjectList();

			 return $items;
		}
		public static function getWaterMeters()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT water_meter.*');

			$query->from('`#__device_table` AS water_meter');

			$query->where('water_meter.state = 1');
			$query->where('water_meter.dio_id > 0' );
			//$query->where('a.type = '.self::$ai );			
			$query->where('water_meter.dio_type IN ('.implode(',',$db->quote(array(self::$water_meter, self::$water_meter_tkd, self::$water_meter_general))).")");
			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);

			 $items = (array) $db->loadObjectList();

			 return $items;
		}
		public static function getSolarMeters()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query->select('DISTINCT solar_meter.*')
				->from('`#__device_table` AS solar_meter')
				->where('solar_meter.state = 1')				
				->where('solar_meter.dio_type IN ('.implode(',',$db->quote(array(self::$elec_solar_primevolt, self::$elec_solar_shihlinspm8,self::$elec_solar_general))).")");
				
				$db->setQuery($query);

				$items = (array) $db->loadObjectList();
				return $items;
		}
		public static function getElecs()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.*');

			$query->from('`#__device_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.dio_id > 0' );
			//$query->where('a.type = '.self::$ai );
			// $query->where('a.dio_type IN '. $db->quote(array(self::$elec, self::$elec_cic)));
			$query->where('a.dio_type IN ('.implode(',',$db->quote(array(
				self::$elec,
				self::$elec_cic,
				self::$elec_acuvim,
				self::$elec_tatung,
				self::$elec_pem333,
				self::$elec_pem575,
				self::$elec_shihlin,
				self::$elec_cicbaw1a2a,
				self::$elec_weema_1p,
				self::$elec_weema_3p,
				self::$elec_opcda,
				self::$elec_general_opcda,
				self::$elec_aem_drb,
				self::$elec_vmr_mp7))).")");
			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);

			 $items = (array) $db->loadObjectList();

			 return $items;
		}
    public static function updateTempMain($id)
		{
			$db = JFactory::getDbo();

			$fields = array(

				$db->quoteName('main') . ' = ' . $db->quote(0),

				//$db->quoteName('main') . ' = ' . $db->quote($obj->main),

			);

			$conditions = array(
					$db->quoteName('main') . ' = ' . $db->quote(1),
					$db->quoteName('dio_type') . ' = ' . $db->quote(self::$temp),

			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

			$db->setQuery($query)->execute();


			$db = JFactory::getDbo();

			$fields = array(

				$db->quoteName('main') . ' = ' . $db->quote(1),

				//$db->quoteName('main') . ' = ' . $db->quote($obj->main),

			);

			$conditions = array(
					$db->quoteName('id') . ' = ' . $db->quote($id),

			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

			$db->setQuery($query)->execute();
		}
		public static function updateSolarMain($ids)
		{
			$db = JFactory::getDbo();			
			$fields = array(
				$db->quoteName('main') . ' = ' . $db->quote(0),
			);
			$conditions = array(					
					// $db->quoteName('id') . ' IN('.implode(',',$db->quote($ids)).")",
					$db->quoteName('dio_type') . ' IN('.implode(',',$db->quote(array(
						self::$elec_solar_primevolt,						
						self::$elec_solar_shihlinspm8,						
						self::$elec_solar_general,		
						))).")"					
			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);
			$db->setQuery($query)->execute();

			$fields = array(
				$db->quoteName('main') . ' = ' . $db->quote(1),
			);
			$conditions = array(					
					$db->quoteName('id') . ' IN('.implode(',',$db->quote($ids)).")",
					$db->quoteName('dio_type') . ' IN('.implode(',',$db->quote(array(
						self::$elec_solar_primevolt,						
						self::$elec_solar_shihlinspm8,						
						self::$elec_solar_general,	
						))).")"
			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);
			$db->setQuery($query)->execute();


		}
		public static function updateWaterMain($id,$no)
		{
			$db = JFactory::getDbo();

			$fields = array(

				$db->quoteName('main') . ' = ' . $db->quote(0),

				//$db->quoteName('main') . ' = ' . $db->quote($obj->main),

			);

			$conditions = array(
					$db->quoteName('main') . ' = ' . $db->quote($no),					
					$db->quoteName('dio_type') . ' IN('.implode(',',$db->quote(array(
						self::$water_meter,
						self::$water_meter_tkd,
						self::$water_meter_general
						))).")",
					

			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

			$db->setQuery($query)->execute();


			$db = JFactory::getDbo();

			$fields = array(

				$db->quoteName('main') . ' = ' . $db->quote($no),

				//$db->quoteName('main') . ' = ' . $db->quote($obj->main),

			);

			$conditions = array(
					$db->quoteName('id') . ' = ' . $db->quote($id),

			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

			$db->setQuery($query)->execute();
		}
		public static function updateElecMain($id)
		{
			$db = JFactory::getDbo();

			$fields = array(

				$db->quoteName('main') . ' = ' . $db->quote(0),

				//$db->quoteName('main') . ' = ' . $db->quote($obj->main),

			);

			$conditions = array(
					$db->quoteName('main') . ' = ' . $db->quote(1),
					// $db->quoteName('dio_type') . ' IN ' . $db->quote(array(self::$elec, self::$elec_cic)),
					$db->quoteName('dio_type') . ' IN('.implode(',',$db->quote(array(
						self::$elec,
						self::$elec_cic,
						self::$elec_acuvim,
						self::$elec_tatung,
						self::$elec_pem333,
						self::$elec_pem575,
						self::$elec_shihlin,
						self::$elec_cicbaw1a2a,
						self::$elec_opcda,
						self::$elec_general_opcda,
						self::$elec_aem_drb,
						self::$elec_vmr_mp7,
						self::$elec_weema_1p,
						self::$elec_weema_3p
						))).")",
					

			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

			$db->setQuery($query)->execute();


			$db = JFactory::getDbo();

			$fields = array(

				$db->quoteName('main') . ' = ' . $db->quote(1),

				//$db->quoteName('main') . ' = ' . $db->quote($obj->main),

			);

			$conditions = array(
					$db->quoteName('id') . ' = ' . $db->quote($id),

			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

			$db->setQuery($query)->execute();
		}

		public static function getCenterList()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.*');

			$query->from('`#__urllist_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.type = 2');

			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
			 //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;
		}

		public static function get_dio_alarm_top($id)
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
		 ->select('DISTINCT a.*');

		 $query->from('`#__top_table` AS a');

		 $query->where('a.state = 1');
		 $query->where('a.note1 = '.self::$apage);

		 $query->where('a.alarm = '.$id);

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();

		 return $items;
   }

		public static function get_dio_alarmdir_top()
 	 {
 		 $db = JFactory::getDbo();
 		 $query = $db->getQuery(true);

 		 // Select the required fields from the table.
 		$query
 		->select('DISTINCT a.*');

 		$query->from('`#__top_table` AS a');

 		$query->where('a.state = 1');
 		$query->where('a.note1 = '.self::$adpage);

 		$db->setQuery($query);

 		$items = (array) $db->loadObjectList();

 		return $items;
 	}

	public static function file_upload($file,$obj)
	{
		// Clean up filename to get rid of strange characters like spaces etc
		$filename = JFile::makeSafe($file['name']);
		JLog::add("123 ".$filename, JLog::INFO, 'jerror');

		if(empty($filename))    return;

		$ext =  JFile::getExt($filename);
		if(empty($ext))
				$ext = $filename;
		JLog::add("456 ".$ext, JLog::INFO, 'jerror');

		$ext = strtolower($ext);

		$time = date ("YmdHis");

		$myfilename = $time.'-'. $filename;

		$ds = '/';
		// Set up the source and destination of the file
		$src = $file['tmp_name'];
		$path = JPATH_COMPONENT . $ds . "audio";
		$path = "/var/www/html/media" . $ds . "audio";

		$dest1 = $path;

		JLog::add("456 ".$dest1, JLog::INFO, 'jerror');

		$dest = $dest1 . $ds . $myfilename;
		$path1 = '/media/audio';

		$img1 = $path1;

		JLog::add("456 ".$img1, JLog::INFO, 'jerror');

		$img = $img1 . $ds . $myfilename;
/*
		JLog::add($img1, JLog::INFO, 'jerror');
		JLog::add($ds, JLog::INFO, 'jerror');
		JLog::add($img, JLog::INFO, 'jerror');
		*/
		// First check if the file has the right extension, we need jpg only
		if (true)
		{

			 // TODO: Add security checks
			 if(JFolder::exists($path) == false)
			 {

				 JFolder::create($path);
			 }

			 if(JFolder::exists($dest1) == false)
			 {

				 JFolder::create($dest1);
			 }

			 if (JFile::upload($src, $dest))
			 {

				 $obj->alarm_path = $img1;
				 $obj->alarm_file = $myfilename;

					// Redirect to a page of your choice
					JLog::add(JText::_('COM_FLOOR_UPLOAD_OK'), JLog::INFO, 'jerror');
					return true;
			 }
			 else
			 {
					// Redirect and throw an error message
					JLog::add(JText::_('COM_FLOOR_UPLOAD_ERROR'), JLog::WARNING, 'jerror');

			 }

		}
		else
		{
			 // Redirect and notify user file is not right extension
			 JLog::add(JText::_('COM_FLOOR_EXTENSION_ERROR'), JLog::WARNING, 'jerror');
		}

		return false;

	}

}

MyaccountHelpersMyaccount::$apage = 4;
MyaccountHelpersMyaccount::$adpage = 5;

MyaccountHelpersMyaccount::$ai = 3;
MyaccountHelpersMyaccount::$temp = 4;
MyaccountHelpersMyaccount::$elec = 5;
MyaccountHelpersMyaccount::$elec_cic = 9;
MyaccountHelpersMyaccount::$elec_acuvim = 43;
MyaccountHelpersMyaccount::$elec_tatung = 10;
MyaccountHelpersMyaccount::$elec_pem333 = 12;
MyaccountHelpersMyaccount::$elec_pem575 = 13;
MyaccountHelpersMyaccount::$elec_shihlin = 14;
MyaccountHelpersMyaccount::$elec_cicbaw1a2a = 36;
MyaccountHelpersMyaccount::$elec_opcda = 23;
MyaccountHelpersMyaccount::$elec_general_opcda = 31;
MyaccountHelpersMyaccount::$elec_aem_drb = 29;
MyaccountHelpersMyaccount::$elec_weema_1p = 24;
MyaccountHelpersMyaccount::$elec_weema_3p = 25;
MyaccountHelpersMyaccount::$elec_solar_primevolt = 19;
MyaccountHelpersMyaccount::$elec_solar_shihlinspm8 = 33;
MyaccountHelpersMyaccount::$elec_solar_general = 37;
MyaccountHelpersMyaccount::$elec_vmr_mp7 = 21;
MyaccountHelpersMyaccount::$water_meter = 6;
MyaccountHelpersMyaccount::$water_meter_tkd = 20;
MyaccountHelpersMyaccount::$water_meter_general = 32;
