<?php

/**
 * @version    CVS: 1.0.0
 * @package    Com_Water_meter_history
 * <AUTHOR> <<EMAIL>>
 * @copyright  2020 Hu Yu<PERSON>
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
defined('_JEXEC') or die;

JLoader::register('Water_meter_historyHelper', JPATH_ADMINISTRATOR . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'com_water_meter_history' . DIRECTORY_SEPARATOR . 'helpers' . DIRECTORY_SEPARATOR . 'water_meter_history.php');

use \Joomla\CMS\Factory;
use \Joomla\CMS\MVC\Model\BaseDatabaseModel;

/**
 * Class Water_meter_historyFrontendHelper
 *
 * @since  1.6
 */
class Water_meter_historyHelpersWater_meter_history
{
	/**
	 * Get an instance of the named model
	 *
	 * @param   string  $name  Model name
	 *
	 * @return null|object
	 */
	public static function getModel($name)
	{
		$model = null;

		// If the file exists, let's
		if (file_exists(JPATH_SITE . '/components/com_water_meter_history/models/' . strtolower($name) . '.php')) {
			require_once JPATH_SITE . '/components/com_water_meter_history/models/' . strtolower($name) . '.php';
			$model = BaseDatabaseModel::getInstance($name, 'Water_meter_historyModel');
		}

		return $model;
	}
	public static function get_all_water_meter_devices()
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select('*')
			->from('#__device_table as device')
			// ->order($db->escape('id DESC'))		
			->where('device.state = 1')
			->where('device.dio_type = 6 or device.dio_type = 20 or device.dio_type = 32');
		$db->setQuery($query);
		return (array) $db->loadObjectList();
	}
	public static function get_last_hourly_water_meter_record($device_id, $year, $month = 1, $date_of_month = 1,$hour_of_day = 0)
	{
		$db = JFactory::getDbo();
		$target_year = $db->quote($year);
		$target_month = $db->quote($month);
		$target_date_of_month = $db->quote($date_of_month);
		$query = $db->getQuery(true)
			->select('device_id, `year`,`month`,`date_of_month`, hour_of_day, accumulatewaterflow')
			->from('#__water_meter_history as history')
			->order('year desc')
			->order('month desc')
			->order('date_of_month desc')
			->order('hour_of_day desc')
			->where("history.device_id = {$db->quote($device_id)} 
			 and (history.year < {$target_year}
			 or (history.year = {$target_year} and history.month < {$target_month})			 
			 or (history.year = {$target_year} and history.month = {$target_month} and history.date_of_month < {$target_date_of_month})
			 or (history.year = {$target_year} and history.month = {$target_month} and history.date_of_month = {$target_date_of_month} and history.hour_of_day < {$hour_of_day}))
			 "
			 )		
			->setLimit(1);
		$db->setQuery($query);
		return (array) $db->loadObjectList();
	}
	public static function get_last_water_meter_record($device_id, $year, $month = 1, $date_of_month = 1)
	{
		$db = JFactory::getDbo();
		$target_year = $db->quote($year);
		$target_month = $db->quote($month);
		$target_date_of_month = $db->quote($date_of_month);
		$query = $db->getQuery(true)
			->select('device_id, `year`,`month`,`date_of_month`, hour_of_day, accumulatewaterflow')
			->from('#__water_meter_history as history')
			->order('year desc')
			->order('month desc')
			->order('date_of_month desc')
			->order('hour_of_day desc')
			->where("history.device_id = {$db->quote($device_id)} 
			and (history.year < {$target_year}
			or (history.year = {$target_year} and history.month < {$target_month})			 
			or (history.year = {$target_year} and history.month = {$target_month} and history.date_of_month < {$target_date_of_month}))"
			)		
			->setLimit(1);
		$db->setQuery($query);
		return (array) $db->loadObjectList();
	}
	// public static function get_last_water_meter_hourly_record($device_id, $year, $month, $date_of_month)
	// {
	// 	// $timestamp = strtotime(str_pad($year, 4, 0, STR_PAD_LEFT) . "/" . str_pad($month, 2, 0, STR_PAD_LEFT) . "/" . str_pad($date_of_month, 2, 0, STR_PAD_LEFT) . " 00:00:00");
	// 	if ($date_of_month == 1) {
	// 		if ($month > 1) {
	// 			$month = $month -1;
	// 			$date_of_month = 31;
	// 		} else {
	// 			$year = $year -1;
	// 			$month = 12;
	// 			$date_of_month = 31;
	// 		}
	// 	} else 
	// 	{
	// 		$date_of_month = $date_of_month -1;
	// 	}
	// 	$db = JFactory::getDbo();
	// 	$query = $db->getQuery(true)
	// 		->select('device_id, `year`,`month`,`date_of_month`, hour_of_day, accumulatewaterflow')
	// 		->from('#__water_meter_history as water_meter_history')
	// 		// ->order($db->escape('id DESC'))		
	// 		->order($db->escape('device_id, `year`,`month`,`date_of_month`, hour_of_day desc'))
	// 		// ->group($db->escape('device_id, `year`,`month`,`date_of_month`, hour_of_day'))
	// 		// ->where('UNIX_TIMESTAMP(STR_TO_DATE(CONCAT(`year`, \'-\' , `month`, \'-\', date_of_month , \' \', hour_of_day, \':00:00\'), \'%Y-%m-%d %H:%i:%s\')) < ' . $db->quote($timestamp))
	// 		->where(' water_meter_history.device_id = ' . $db->quote($device_id))
	// 		->where('water_meter_history.year = ' . $db->quote($year))
	// 		->where('water_meter_history.month = ' . $db->quote($month))
	// 		->where('water_meter_history.date_of_month = ' . $db->quote($date_of_month))
	// 		->where('water_meter_history.hour_of_day = ' . $db->quote(23))
	// 		->setLimit(1);
	// 	$db->setQuery($query);
	// 	return (array) $db->loadObjectList();
	// }
	public static function get_water_meter_info($device_id)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select('*')
			->from('#__device_table as device')
			->where('device.id = ' . $db->quote($device_id));
		$db->setQuery($query);
		$result = (array) $db->loadObjectList();
		if (count($result) > 0)
		{
			return $result[0];
		}
		return new StdClass();
	}	
	public static function get_hourly_water_meter_statistics($device_id, $year, $month, $date_of_month)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select('device_id, `year`,`month`,`date_of_month`, hour_of_day, CAST(accumulatewaterflow as DECIMAL(10,2)) as accumulatewaterflow, update_time')
			->from('#__water_meter_history as water_meter_history')
			// ->order($db->escape('id DESC'))		
			->order($db->escape('device_id, `year`,`month`,`date_of_month`, hour_of_day asc'))
			->group($db->escape('device_id, `year`,`month`,`date_of_month`, hour_of_day'))
			->where('water_meter_history.device_id = ' . $db->quote($device_id))
			->where('water_meter_history.year = ' . $db->quote($year))
			->where('water_meter_history.month = ' . $db->quote($month))
			->where('water_meter_history.date_of_month = ' . $db->quote($date_of_month));
		$db->setQuery($query);
		return (array) $db->loadObjectList();
	}
	public static function get_daily_water_meter_statistics($device_id, $year, $month)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select('device_id, `year`,`month`,`date_of_month`, CAST(max(accumulatewaterflow) as DECIMAL(10,2)) AS maxAccumulateWaterFlow, CAST(MIN(accumulatewaterflow) as DECIMAL(10,2)) AS minaccumulatewaterflow, CAST(max(accumulatewaterflow)-MIN(accumulatewaterflow) as DECIMAL(10,2)) AS diff')
			->from('#__water_meter_history as water_meter_history')
			// ->order($db->escape('id DESC'))		
			->order($db->escape('device_id, `year`,`month`,`date_of_month` asc'))
			->group($db->escape('device_id, `year`,`month`,`date_of_month`'))
			->where('water_meter_history.device_id = ' . $db->quote($device_id))
			->where('water_meter_history.year = ' . $db->quote($year))
			->where('water_meter_history.month = ' . $db->quote($month));
		$db->setQuery($query);
		return (array) $db->loadObjectList();
	}
	public static function get_monthly_water_meter_statistics($device_id, $year)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select('device_id, `year`,`month`')
			->from('#__water_meter_history as water_meter_history')
			// ->order($db->escape('id DESC'))		
			->order($db->escape('device_id, `year`,`month` asc'))
			->group($db->escape('device_id, `year`,`month`'))
			->where('water_meter_history.device_id = ' . $db->quote($device_id))
			->where('water_meter_history.year = ' . $db->quote($year));
		$db->setQuery($query);
		$result = (array) $db->loadObjectList();
		
		
		// $query = $db->getQuery(true)
		// ->select('*')
		// ->from("#__water_meter_history as water_meter_history")
		// ->group($db->escape('month'))
		// ->order($db->escape('month desc'))
		// ->order($db->escape('date_of_month desc'))
		// ->order($db->escape('hour_of_day desc'))
		// ->where('water_meter_history.device_id = ' . $db->quote($device_id))
		// ->where('water_meter_history.year = ' . $db->quote($year));
		// $db->setQuery($query);
		// $maxs = (array) $db->loadObjectList();
		
		// $query = $db->getQuery(true)
		// ->select('*')
		// ->from("#__water_meter_history as water_meter_history")
		// ->group($db->escape('month'))
		// ->order($db->escape('month asc'))
		// ->order($db->escape('date_of_month asc'))
		// ->order($db->escape('hour_of_day asc'))
		// ->where('water_meter_history.device_id = ' . $db->quote($device_id))
		// ->where('water_meter_history.year = ' . $db->quote($year));
		// $mins = (array) $db->loadObjectList();
		

		foreach ($result as $key => $value) {
			$result[$key]->maxAccumulateWaterFlow = 0;
			$result[$key]->minaccumulatewaterflow = 0;
			$query = $db->getQuery(true)
			->select('*')
			->from("#__water_meter_history as water_meter_history")			
			->order($db->escape('month desc'))
			->order($db->escape('date_of_month desc'))
			->order($db->escape('hour_of_day desc'))
			->where('year = ' . $db->quote($year))
			->where('month = ' . $db->quote($value->month))
			->where('water_meter_history.device_id = ' . $db->quote($device_id))
			->setLimit(1);
			$db->setQuery($query);
			$max = (array) $db->loadObjectList();
			if (count($max) > 0)
			{
				$result[$key]->maxAccumulateWaterFlow = $max[0]->accumulatewaterflow;
			}

			$query = $db->getQuery(true)
			->select('*')
			->from("#__water_meter_history as water_meter_history")
			->order($db->escape('month asc'))
			->order($db->escape('date_of_month asc'))
			->order($db->escape('hour_of_day asc'))
			->where('year = ' . $db->quote($year))
			->where('month = ' . $db->quote($value->month))
			->where('water_meter_history.device_id = ' . $db->quote($device_id))
			->setLimit(1);
			$db->setQuery($query);
			$min = (array) $db->loadObjectList();
			if (count($min) > 0)
			{
				$result[$key]->minaccumulatewaterflow = $min[0]->accumulatewaterflow;
			}
			// foreach ($maxs as $key1 => $value1) {
			// 	if ($value1->month == $value->month)
			// 	{
			// 		$result[$key]->maxAccumulateWaterFlow = $value1->accumulatewaterflow;
			// 	}
			// }
			// foreach ($mins as $key1 => $value1) {
			// 	if ($value1->month == $value->month)
			// 	{
			// 		$result[$key]->minaccumulatewaterflow = $value1->accumulatewaterflow;
			// 	}
			// }
			$result[$key]->diff = $result[$key]->maxAccumulateWaterFlow - $result[$key]->minaccumulatewaterflow;
		}
		return $result;
	}
	public static function get_annual_water_meter_statistics($device_id)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select('device_id, `year`')
			// ->select('id')
			->from('#__water_meter_history as water_meter_history')
			// ->order($db->escape('id DESC'))		
			->order($db->escape('device_id, `year` asc'))
			->group($db->escape('device_id, `year`'))
			->where('water_meter_history.device_id = ' . $db->quote($device_id));
		$db->setQuery($query);
		$result = (array) $db->loadObjectList();
		
		
		// $query = $db->getQuery(true)
		// ->select('*')
		// ->from("#__water_meter_history as water_meter_history")
		// ->group($db->escape('year'))
		// ->order($db->escape('year desc'))
		// ->order($db->escape('month desc'))
		// ->order($db->escape('date_of_month desc'))
		// ->order($db->escape('hour_of_day desc'))
		// ->where('water_meter_history.device_id = ' . $db->quote($device_id));
		// $db->setQuery($query);
		// $maxs = (array) $db->loadObjectList();
		
		// $query = $db->getQuery(true)
		// ->select('*')
		// ->from("#__water_meter_history as water_meter_history")
		// ->group($db->escape('year'))
		// ->order($db->escape('year asc'))
		// ->order($db->escape('month asc'))
		// ->order($db->escape('date_of_month asc'))
		// ->order($db->escape('hour_of_day asc'))
		// ->where('water_meter_history.device_id = ' . $db->quote($device_id));
		// $db->setQuery($query);
		// $mins = (array) $db->loadObjectList();
		

		foreach ($result as $key => $value) {
			$result[$key]->maxAccumulateWaterFlow = 0;
			$result[$key]->minaccumulatewaterflow = 0;
			
			$query = $db->getQuery(true)
			->select('*')
			->from("#__water_meter_history as water_meter_history")
			->order($db->escape('year desc'))
			->order($db->escape('month desc'))
			->order($db->escape('date_of_month desc'))
			->order($db->escape('hour_of_day desc'))
			->where('year = ' . $db->quote($value->year))
			->where('water_meter_history.device_id = ' . $db->quote($device_id))
			->setLimit(1);
			$db->setQuery($query);
			$max = (array) $db->loadObjectList();
			if (count($max) > 0)
			{
				$result[$key]->maxAccumulateWaterFlow = $max[0]->accumulatewaterflow;
			}

			$query = $db->getQuery(true)
			->select('*')
			->from("#__water_meter_history as water_meter_history")
			->order($db->escape('year asc'))
			->order($db->escape('month asc'))
			->order($db->escape('date_of_month asc'))
			->order($db->escape('hour_of_day asc'))
			->where('year = ' . $db->quote($value->year))
			->where('water_meter_history.device_id = ' . $db->quote($device_id))
			->setLimit(1);
			$db->setQuery($query);
			$min = (array) $db->loadObjectList();
			if (count($min) > 0)
			{
				$result[$key]->minaccumulatewaterflow = $min[0]->accumulatewaterflow;
			}
			// foreach ($maxs as $key1 => $value1) {
			// 	if ($value1->year == $value->year)
			// 	{
			// 		$result[$key]->maxAccumulateWaterFlow = $value1->accumulatewaterflow;
			// 	}
			// }
			// foreach ($mins as $key1 => $value1) {
			// 	if ($value1->year == $value->year)
			// 	{
			// 		$result[$key]->minaccumulatewaterflow = $value1->accumulatewaterflow;
			// 	}
			// }
			$result[$key]->diff = $result[$key]->maxAccumulateWaterFlow - $result[$key]->minaccumulatewaterflow;
		}
		return $result;
	}
	/**
	 * Gets the files attached to an item
	 *
	 * @param   int     $pk     The item's id
	 *
	 * @param   string  $table  The table's name
	 *
	 * @param   string  $field  The field's name
	 *
	 * @return  array  The files
	 */
	public static function getFiles($pk, $table, $field)
	{
		$db = Factory::getDbo();
		$query = $db->getQuery(true);

		$query
			->select($field)
			->from($table)
			->where('id = ' . (int) $pk);

		$db->setQuery($query);

		return explode(',', $db->loadResult());
	}

	/**
	 * Gets the edit permission for an user
	 *
	 * @param   mixed  $item  The item
	 *
	 * @return  bool
	 */
	public static function canUserEdit($item)
	{
		$permission = false;
		$user       = Factory::getUser();

		if ($user->authorise('core.edit', 'com_water_meter_history')) {
			$permission = true;
		} else {
			if (isset($item->created_by)) {
				if ($user->authorise('core.edit.own', 'com_water_meter_history') && $item->created_by == $user->id) {
					$permission = true;
				}
			} else {
				$permission = true;
			}
		}

		return $permission;
	}
}
