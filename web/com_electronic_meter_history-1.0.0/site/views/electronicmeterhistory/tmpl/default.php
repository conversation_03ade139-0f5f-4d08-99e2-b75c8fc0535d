<?php

/**
 * @version    CVS: 1.0.0
 * @package    Com_Electronic_meter_history
 * <AUTHOR> <<EMAIL>>
 * @copyright  2020 Hu Yuwei
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
// No direct access
defined('_JEXEC') or die;

use \Joomla\CMS\HTML\HTMLHelper;
use \Joomla\CMS\Factory;
use \Joomla\CMS\Uri\Uri;
use \Joomla\CMS\Router\Route;
use \Joomla\CMS\Language\Text;

HTMLHelper::addIncludePath(JPATH_COMPONENT . '/helpers/html');
HTMLHelper::_('bootstrap.tooltip');
HTMLHelper::_('behavior.multiselect');
HTMLHelper::_('formbehavior.chosen', 'select');

$user       = Factory::getUser();
$userId     = $user->get('id');
// $listOrder  = $this->state->get('list.ordering');
// $listDirn   = $this->state->get('list.direction');
$canCreate  = $user->authorise('core.create', 'com_electronic_meter_history') && file_exists(JPATH_COMPONENT . DIRECTORY_SEPARATOR . 'models' . DIRECTORY_SEPARATOR . 'forms' . DIRECTORY_SEPARATOR . 'tableform.xml');
$canEdit    = $user->authorise('core.edit', 'com_electronic_meter_history') && file_exists(JPATH_COMPONENT . DIRECTORY_SEPARATOR . 'models' . DIRECTORY_SEPARATOR . 'forms' . DIRECTORY_SEPARATOR . 'tableform.xml');
$canCheckin = $user->authorise('core.manage', 'com_electronic_meter_history');
$canChange  = $user->authorise('core.edit.state', 'com_electronic_meter_history');
$canDelete  = $user->authorise('core.delete', 'com_electronic_meter_history');

// Import CSS
$document = Factory::getDocument();
$document->addStyleSheet(Uri::root() . 'media/com_electronic_meter_history/css/list.css');

if ($user->id == 0) {
	JError::raiseWarning(403, JText::_('COM_ELECTRONIC_METER_ERROR_MUST_LOGIN'));
	$joomlaLoginUrl = 'index.php?option=com_users&view=login';

	echo "<br><a href='" . JRoute::_($joomlaLoginUrl) . "'>" . JText::_('COM_ELECTRONIC_METER_LOG_IN') . "</a><br>";
	return;
}

?>
<script src="/media/com_electronic_meter_history/js/vue.min.js"></script>
<script src="/media/com_electronic_meter_history/js/Chart.min.js"></script>
<script src="/media/com_electronic_meter_history/js/jspdf.min.js"></script>
<script src="/media/com_electronic_meter_history/js/html2canvas.js"></script>
<!-- <script src="/media/com_electronic_meter_history/js/axios.js"></script> -->
<script src="/media/com_electronic_meter_history/js/FileSaver.min.js"></script>
<script src="/media/com_electronic_meter_history/js/xlsx.full.min.js"></script>

<link rel="stylesheet" href="/media/com_electronic_meter_history/css/table.css">
<div class="page-header" style="text-align:center;">
	<h1>電力歷史紀錄<small></small></h1>
</div>
<div style="min-height:600px">
	<div class="" style="text-align:center;">
		<form action="">		
			<select id="meter_type" name="meter_type" class="form-control">
				<option>請選擇類別</option>
				<option value="ElectronicMeter">電表</option>
				<option value="SolarMeter">太陽能</option>
			</select>
			<div style="display:none;">
				<select class="form-control" id="device_id" >
					<optgroup id="optg_electronic_meter" label="電表">
					<?php
					$electronic_meter_devices = Electronic_meter_historyHelpersElectronic_meter_history::get_all_electronic_meter_devices();
					foreach ($electronic_meter_devices as $electronic_meter_device) : ?>
						<option value="<?php echo ($electronic_meter_device->id); ?>"><?php echo ($electronic_meter_device->info); ?> </option>
					<?php endforeach; ?>
					</optgroup>
					<optgroup id="optg_solar_meter" label="太陽能">
					<?php
					$solar_meter_devices = Electronic_meter_historyHelpersElectronic_meter_history::get_all_solar_meter_devices();
					foreach ($solar_meter_devices as $solar_meter_device) : ?>
						<option value="<?php echo ($solar_meter_device->id); ?>"><?php echo ($solar_meter_device->info); ?> </option>
					<?php endforeach; ?>
					</optgroup>
				</select>
			</div>			

			<br/><input class="form-control" id="date" type="date" />
			<!-- <input type="number" id="device_id" name="device_id" value="928" placeholder="device_id" /> -->
			<!-- <input class="form-control"  type="number" id="year" name="year" value="2020" placeholder="Year" />
		<input class="form-control"  type="number" id="month" name="month" value="1" placeholder="Month" />
		<input class="form-control"  type="number" id="date_of_month" name="date_of_month" value="1" placeholder="Date of month" /> -->
			<!-- <input class="form-control"  type="number" id="range_type" name="range_type" value="1" placeholder="range_type" /> -->
			<br/>
			<a href="#" class="btn btn-success btn-small" onclick="return submit()"><i class="icon-list"></i>查詢</a>
			<!-- <a href="#" class="btn btn-success btn-small" onclick="return export_xlsx()"><i class="icon-download"></i>匯出</a> -->
		</form>
	</div>
	<div id="chart-div" class="chart-div" style="display:none">
		<div class="tabbable" style="margin-left:auto; margin-right:auto;"> <!-- Only required for left/right tabs -->
			<ul class="nav nav-tabs">
				<li class="active"><a href="#tab1" data-toggle="tab">歷史紀錄</a></li>
				<li><a href="#tab2" data-toggle="tab">時間電價</a></li>
			</ul>
			<div class="tab-content">
				<div class="tab-pane active" id="tab1">
						<table>
							<thead>
								<tr>
									<th colspan="3">
										<h3 class="meter-type">時用電量</h3>
									</th>
								</tr>
								<tr>
									<th style="width:200px;"></th>
									<th style="min-width:500px;"></th>
									<th></th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td></td>
									<td>
										<table class="paleBlueRows">
											<thead>
												<tr>
													<th  class="meter-type" style="width:150px;">區間</th>
													<th  class="meter-type" style="width:150px;">區間用電量</th>
													<th  class="meter-type" style="width:150px;">累積用電量</th>
												</tr>
											</thead>
											<tbody id="hourlyTbody">

											</tbody>
										</table>
									</td>
									<td>
										<div class="chart-canvas-container">
											<canvas class="chart-canvas" id="hourlyChart"></canvas>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
						<table>
							<thead>
								<tr>
									<th colspan="3">
										<h3  class="meter-type">日用電量</h3>
									</th>
								</tr>
								<tr>
									<th style="width:200px;"></th>
									<th style="min-width:500px;"></th>
									<th></th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td></td>
									<td>
										<table class="paleBlueRows">
											<thead>
												<tr>
													<th  class="meter-type" style="width:150px;">區間</th>
													<th  class="meter-type" style="width:150px;">區間用電量</th>
													<th  class="meter-type" style="width:150px;">累積用電量</th>
												</tr>
											</thead>
											<tbody id="dailyTbody">

											</tbody>
										</table>
									</td>
									<td>
										<div class="chart-canvas-container">
											<canvas class="chart-canvas" id="dailyChart"></canvas>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
						<table>
							<thead>
								<tr>
									<th colspan="3">
										<h3  class="meter-type">月用電量</h3>
									</th>
								</tr>
								<tr>
									<th style="width:200px;"></th>
									<th style="min-width:500px;"></th>
									<th></th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td></td>
									<td>
										<table class="paleBlueRows">
											<thead>
												<tr>
													<th  class="meter-type" style="width:150px;">區間</th>
													<th  class="meter-type" style="width:150px;">區間用電量</th>
													<th  class="meter-type" style="width:150px;">累積用電量</th>
												</tr>
											</thead>
											<tbody id="monthlyTbody">

											</tbody>
										</table>
									</td>
									<td>
										<div class="chart-canvas-container">
											<canvas class="chart-canvas" id="monthlyChart"></canvas>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
						<table>
							<thead>
								<tr>
									<th colspan="3">
										<h3  class="meter-type">年用電量</h3>
									</th>
								</tr>
								<tr>
									<th style="width:200px;"></th>
									<th style="min-width:500px;"></th>
									<th></th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td></td>
									<td>
										<table class="paleBlueRows">
											<thead>
												<tr>
													<th  class="meter-type" style="width:150px;">區間</th>
													<th  class="meter-type" style="width:150px;">區間用電量</th>
													<th  class="meter-type" style="width:150px;">累積用電量</th>
												</tr>
											</thead>
											<tbody id="annualTbody">

											</tbody>
										</table>
									</td>
									<td>
										<div class="chart-canvas-container">
											<canvas class="chart-canvas" id="annualChart"></canvas>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
				</div>


				<div class="tab-pane" id="tab2">	
					<div id="hourly_price_tbody" >
					<div class="well" style="width:60%;margin-left:auto;margin-right:auto;">
						<h4 title="6月1日至9月30日">夏月電價*</h4>
					尖峰電費(每度)：<input type="number" v-model="summer_peak" step="0.01" min="0.01" />
					半尖峰電費(每度)：<input type="number" v-model="summer_mid" step="0.01" min="0.01" />
					離峰電費(每度)：<input type="number" v-model="summer_off_peak" step="0.01" min="0.01"  />					
					</div>
					<div class="well" style="width:60%;margin-left:auto;margin-right:auto;">
						<h4 title="夏月以外時間">非夏月電價*</h4>
					尖峰電費(每度)：<input type="number" v-model="not_summer_peak" step="0.01" min="0.01"  />
					半尖峰電費(每度)：<input type="number" v-model="not_summer_mid" step="0.01" min="0.01"  />
					離峰電費(每度)：<input type="number" v-model="not_summer_off_peak" step="0.01" min="0.01"  />					
					
					</div>
					<div class="well" style="width:60%;margin-left:auto;margin-right:auto;">
						<button @click="getData">計算電價</button>
						<button @click="exportPdf">匯出</button>
					</div>
						<!-- 每度費用：<input type="number" step="0.01" min="0.01" v-model="unitPrice"/> -->
						<div id="report_div" style="width: 40%;margin-left: auto;margin-right: auto;padding-bottom:20px;" v-if="show_table">
							<table v-if="pricing != null" class="paleBlueRows" style="margin-left:auto; margin-right:auto;">
								<thead>
									<tr>
										<th>電表</th>
										<th colspan="2">夏月電價</th>										
										<th colspan="2">非夏月電價</th>										
									</tr>
								</thead>
								<tbody>									
									<tr>
										<td>{{device_name}}</td>
										<td>尖峰電費(每度)：</td>
										<td>{{pricing.summer.peak}}</td>										
										<td>尖峰電費(每度)：</td>
										<td>{{pricing.not_summer.peak}}</td>										
									</tr>
									<tr>
										<th>日期</th>
										<td>半尖峰電費(每度)：</td>
										<td>{{pricing.summer.mid}}</td>										
										<td>半尖峰電費(每度)：</td>
										<td>{{pricing.not_summer.mid}}</td>										
									</tr>
									<tr>
										<td>{{selected_date}}</td>
										<td>離峰電費(每度)：</td>
										<td>{{pricing.summer.off_peak}}</td>
										<td>離峰電費(每度)：</td>
										<td>{{pricing.not_summer.off_peak}}</td>
									</tr>
								</tbody>
							</table>
							<table class="paleBlueRows" style="margin-left:auto; margin-right:auto;">
								<thead>
									<tr>
										<th>時</th>
										<th>時用量</th>
										<!-- <th>累積電量</th> -->
										<th>計算費用</th>
										<th>&nbsp;&nbsp;&nbsp;&nbsp;</th>
										<th>日</th>
										<th>日用量</th>
										<!-- <th>累積電量</th> -->
										<th>計算費用</th>
										<th>&nbsp;&nbsp;&nbsp;&nbsp;</th>
										<th>月</th>
										<th>月用量</th>
										<!-- <th>累積用量</th> -->
										<th>計算費用</th>
										<th>&nbsp;&nbsp;&nbsp;&nbsp;</th>
										<th>年</th>
										<th>年用量</th>
										<!-- <th>累積電量</th> -->
										<th>計算費用</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="(n,index) in maxLength">								
									<td>
											<span v-if="date_of_month.length > index">
												{{date_of_month[index].hour_of_day}}
											</span>
											<span v-else>
											&nbsp;
											</span>									
										</td>
										<td>
											<span v-if="date_of_month.length > index">											
												{{date_of_month[index].total_usage}}
											</span>
											<span v-else>
											&nbsp;
											</span>			
										</td>
										<td>
											<span v-if="date_of_month.length > index">																						
												{{date_of_month[index].total_price}}
											</span>
											<span v-else>
											&nbsp;
											</span>												
										</td>
										
										<td>
										&nbsp;
										</td>
										<td>
											<span v-if="month.length > index">
												{{month[index].date_of_month}}
											</span>
											<span v-else>
											&nbsp;
											</span>									
										</td>
										<td>
											<span v-if="month.length > index">											
												{{month[index].total_usage}}
											</span>
											<span v-else>
											&nbsp;
											</span>			
										</td>
							
										<td>
											<span v-if="month.length > index">																						
												{{month[index].total_price}}
											</span>
											<span v-else>
											&nbsp;
											</span>												
										</td>
										<td>
										&nbsp;
										</td>
										<td>
											<span v-if="year.length > index">
												{{year[index].month}}
											</span>
											<span v-else>
											&nbsp;
											</span>												
										</td>
										<td>
											<span v-if="year.length > index">											
												{{year[index].total_usage}}
											</span>
											<span v-else>
											&nbsp;
											</span>			
										</td>
										<td>
											<span v-if="year.length > index">											
												{{year[index].total_price}}
											</span>
											<span v-else>
											&nbsp;
											</span>										
										</td>
										<td>&nbsp;</td>
										<td>
											<span v-if="annual.length > index">
												{{annual[index].year}}
											</span>
											<span v-else>
											&nbsp;
											</span>								
										</td>
										<td>
											<span v-if="annual.length > index">											
												{{annual[index].total_usage}}
											</span>	
											<span v-else>
												&nbsp;
											</span>
										</td>
										<td>
											<span v-if="annual.length > index">											
												{{annual[index].total_price}}
											</span>
											<span v-else>
											&nbsp;
											</span>												
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>


</div>
<script src="/media/com_electronic_meter_history/js/drawChart.js?d=20220812"></script>
<style>
	.chart-div {
		width: 100%;
		text-align: center;
	}

	.chart-canvas-container {
		width: 1000px;
		text-align: center;
	}

	canvas {
		width: 1000px;
		margin: 0 auto;
	}
</style>