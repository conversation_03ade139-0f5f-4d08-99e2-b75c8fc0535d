<?php

/**
 * @version    CVS: 1.0.0
 * @package    Com_Electronic_meter_history
 * <AUTHOR> <<EMAIL>>
 * @copyright  2020 Hu Yuwei
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
// No direct access
defined('_JEXEC') or die;

jimport('joomla.application.component.controller');

use \Joomla\CMS\Factory;

/**
 * Class Electronic_meter_historyController
 *
 * @since  1.6
 */
class Electronic_meter_historyController extends \Joomla\CMS\MVC\Controller\BaseController
{
	/**
	 * Method to display a view.
	 *
	 * @param   boolean $cachable  If true, the view output will be cached
	 * @param   mixed   $urlparams An array of safe url parameters and their variable types, for valid values see {@link JFilterInput::clean()}.
	 *
	 * @return  JController   This object to support chaining.
	 *
	 * @since    1.5
	 * @throws Exception
	 */
	public function display($cachable = false, $urlparams = false)
	{
		$app  = Factory::getApplication();
		$view = $app->input->getCmd('view', 'electronicmeterhistory');
		$app->input->set('view', $view);

		parent::display($cachable, $urlparams);

		return $this;
	}
	public function getElectronicMeterInfo()
	{
		$app  = JFactory::getApplication();
		$device_id = $app->input->getInt('device_id', -1);
		$result = Electronic_meter_historyHelpersElectronic_meter_history::get_electronic_meter_info($device_id);
		echo (json_encode($result));
		JFactory::getApplication()->close();
	}
	public function getElectronicMeterStatisticsByPeriod()
	{
		$summer = new StdClass();
		$summer->peak = 2.32;
		$summer->mid = 1.42;
		$summer->off_peak = 0.91;
		$not_summer = new StdClass();
		$not_summer->peak = 2.24;
		$not_summer->mid = 1.35;
		$not_summer->off_peak = 0.84;

		$pricing = new StdClass();
		$pricing->summer = $summer;
		$pricing->not_summer = $not_summer;
		
		$app  = JFactory::getApplication();
		$pricing->summer->peak = $app->input->getVar('summer_peak_price',$pricing->summer->peak);
		$pricing->summer->mid = $app->input->getVar('summer_mid_price',$pricing->summer->mid);
		$pricing->summer->off_peak = $app->input->getVar('summer_off_peak_price',$pricing->summer->off_peak);
		$pricing->not_summer->peak = $app->input->getVar('not_summer_peak_price',$pricing->not_summer->peak);
		$pricing->not_summer->mid = $app->input->getVar('not_summer_mid_price',$pricing->not_summer->mid);
		$pricing->not_summer->off_peak = $app->input->getVar('not_summer_off_peak_price',$pricing->not_summer->off_peak);

		$range_type = $app->input->getInt('range_type', -1);
		$device_id = $app->input->getInt('device_id', -1);
		$year = $app->input->getInt('year', -1);
		$month = $app->input->getInt('month', -1);
		$date_of_month = $app->input->getInt('date_of_month', -1);
		$hour_of_day = $app->input->getInt('hour_of_day', -1);
		$items = new StdClass();
		$items->pricing = $pricing;
		$items->annual =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,-1,-1,-1,-1);
		$items->year =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,-1,-1,-1);
		$items->month =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,$month,-1,-1);
		$items->date_of_month =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,$month,$date_of_month,-1);
		// $items->hour_of_day =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,$month,$date_of_month,-1);
		echo(json_encode($items));
		$app->close();

	}
	public function getElectronicMeterStatisticsBySeason()
	{
		$app  = JFactory::getApplication();
		$range_type = $app->input->getInt('range_type', -1);
		$device_id = $app->input->getInt('device_id', -1);
		$year = $app->input->getInt('year', -1);
		$month = $app->input->getInt('month', -1);
		$date_of_month = $app->input->getInt('date_of_month', -1);
		
		$summer = new StdClass();
		$summer->peak = 2.32;
		$summer->mid = 1.42;
		$summer->off_peak = 0.91;
		$not_summer = new StdClass();
		$not_summer->peak = 2.24;
		$not_summer->mid = 1.35;
		$not_summer->off_peak = 0.84;

		$p = array($summer, $not_summer);
		
		if ($range_type == 0) //hourly
		{
			$result = new stdClass();
			$result->items = Electronic_meter_historyHelpersElectronic_meter_history::get_hourly_electronic_meter_statistics_by_period($device_id, $year, $month, $date_of_month);		
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month, $date_of_month);			
			$result->lastRecord = count($lastRecord) >0 ? $lastRecord[0] : null;		
			echo (json_encode($result));
		} else if ($range_type == 1) //daily
		{
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_daily_electronic_meter_statistics($device_id, $year, $month);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month, $date_of_month);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minaccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minaccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minaccumulatepower, 3,".","");
			}
			echo (json_encode($records));
		} else if ($range_type == 2) //monthly
		{
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_monthly_electronic_meter_statistics($device_id, $year);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minaccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minaccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minaccumulatepower, 3,".","");
			}
			echo (json_encode($records));
		} else if ($range_type == 3) // yearly
		{
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_annual_electronic_meter_statistics($device_id);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minAccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minAccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minAccumulatepower, 3,".","");
			}
			echo (json_encode($records));
		}
		JFactory::getApplication()->close();
	}
	public function getElectronicMeterStatistics()
	{
		$app  = JFactory::getApplication();
		$range_type = $app->input->getInt('range_type', -1);
		$device_id = $app->input->getInt('device_id', -1);
		$year = $app->input->getInt('year', -1);
		$month = $app->input->getInt('month', -1);
		$date_of_month = $app->input->getInt('date_of_month', -1);

		if ($range_type == 0) //hourly
		{
			$result = new stdClass();
			$result->items = Electronic_meter_historyHelpersElectronic_meter_history::get_hourly_electronic_meter_statistics($device_id, $year, $month, $date_of_month);		
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month, $date_of_month);			
			$result->lastRecord = count($lastRecord) >0 ? $lastRecord[0] : null;		
			echo (json_encode($result));
		} else if ($range_type == 1) //daily
		{
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_daily_electronic_meter_statistics($device_id, $year, $month);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month, $date_of_month);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minaccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minaccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minaccumulatepower, 3,".","");
			}
			echo (json_encode($records));
		} else if ($range_type == 2) //monthly
		{
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_monthly_electronic_meter_statistics($device_id, $year);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minaccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minaccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minaccumulatepower, 3,".","");
			}
			echo (json_encode($records));
		} else if ($range_type == 3) // yearly
		{
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_annual_electronic_meter_statistics($device_id);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minAccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minAccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minAccumulatepower, 3,".","");
			}
			echo (json_encode($records));
		}
		JFactory::getApplication()->close();
	}
}
