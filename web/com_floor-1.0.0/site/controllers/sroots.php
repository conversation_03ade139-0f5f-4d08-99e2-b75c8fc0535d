<?php
/**
 * @version    CVS: 1.0.0
 * @package    Com_Floor
 * <AUTHOR> lo <<EMAIL>>
 * @copyright  2018 kevin lo
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */

// No direct access.
defined('_JEXEC') or die;

/**
 * Tables list controller class.
 *
 * @since  1.6
 */
class FloorControllerSRoots extends FloorController
{
	/**
	 * Proxy for getModel.
	 *
	 * @param   string  $name    The model name. Optional.
	 * @param   string  $prefix  The class prefix. Optional
	 * @param   array   $config  Configuration array for model. Optional
	 *
	 * @return object	The model
	 *
	 * @since	1.6
	 */
	public function &getModel($name = 'SRoots', $prefix = 'FloorModel', $config = array())
	{
		$model = parent::getModel($name, $prefix, array('ignore_request' => true));

		return $model;
	}

	public function getSOSItems()
	{
	
		$ret = TopHelpersUtility::getSOSItems();
		
		echo($ret);
		JFactory::getApplication()->close();
	}
	public function getBaIPCamItems()
	{	
		system("mysqldump -h localhost -u joomlauser --password=123456 joomladb  xwzms_ipcam_table > /tmp/backup.sql");
		
		echo file_get_contents("/tmp/backup.sql");
		//system("bash /data/backup/restore.sh /data/backup/".$file);
		
		JFactory::getApplication()->close();
	}	
	public function getBaSOSItems()
	{	
		$db = JFactory::getDbo();

		$table = $db->getPrefix()."ipcam_table ".$db->getPrefix()."device_table";
		system("mysqldump -h localhost -u joomlauser --password=123456 joomladb  ".$table." > /tmp/backup.sql");
		
		echo file_get_contents("/tmp/backup.sql");
		//system("bash /data/backup/restore.sh /data/backup/".$file);
		
		JFactory::getApplication()->close();
	}
	public function all_restore()
	{
		$app  = JFactory::getApplication();

		$file = $app->input->getVar('file',"1");
		system("bash /data/backup/restore.sh /data/backup/".$file);
		echo("OK");
		
		JFactory::getApplication()->close();
	}
	public function all_backup()
	{
		
		system("bash /data/backup/backup.sh");
		echo("OK");
		
		JFactory::getApplication()->close();
	}
	public function app6_login()
	{
		$app  = JFactory::getApplication();

		$user = $app->input->getVar('USERNAME',"");
		
		$json =  "LOGINOK";//json_encode($obj);

		echo($json);
		
		JFactory::getApplication()->close();
	}

	public function getPhoneItems()
	{
		$app  = JFactory::getApplication();

		$id = $app->input->getInt('id',0);

		$items = TopHelpersUtility::getPhoneItems($id);

		$json = json_encode($items);

		echo($json);
		
		JFactory::getApplication()->close();
	}
	public function getBaBcItems()
    {	
		$items = TopHelpersUtility::getBaBcItems();

		echo($items);

	    JFactory::getApplication()->close();
    }
	public function getBcItems()
    {
		
		$items = TopHelpersUtility::getBcItems();
		
		$json = json_encode($items);

		echo($json);

	    JFactory::getApplication()->close();
	}
	public function getIPCamItems()
    {
		
		$items = TopHelpersUtility::getIPCamItems();
		
		$json = json_encode($items);

		echo($json);

	    JFactory::getApplication()->close();
	}	
	public function getBaLimitcallItem()
    {
		$app  = JFactory::getApplication();

		$name = $app->input->getVar('range','');
		
		$items = TopHelpersUtility::getBaLimitcallItem($name);

		echo($items);

	    JFactory::getApplication()->close();
    }
	public function getLimitcallItem()
    {
		$app  = JFactory::getApplication();

		$name = $app->input->getVar('range','');
		
		$items = TopHelpersUtility::getLimitcallItem($name);
		
		$json = json_encode($items);

		echo($json);

	    JFactory::getApplication()->close();
	}
	public function getBaBcItem()
    {
		$app  = JFactory::getApplication();

		$name = $app->input->getVar('name','');
		
		$items = TopHelpersUtility::getBaBcItem($name);

		echo($items);

	    JFactory::getApplication()->close();
    }
	public function getBcItem()
    {
		$app  = JFactory::getApplication();

		$name = $app->input->getVar('name','');
		
		$items = TopHelpersUtility::getBcItem($name);
		
		if(count($items))
			$json = json_encode($items[0]);
		else 
		    $json = '{}';	

		echo($json);

	    JFactory::getApplication()->close();
    }	
	public function getElecItem()
    {
		$app  = JFactory::getApplication();

		$name = $app->input->getVar('name','');
		
		$id = 0;
		if($name)
		{
			$items = TopHelpersUtility::getElecItem($name);
			
			if(count($items))
			    $id = $items[0]->id;
		}
		
		$json = json_encode($id);

		echo($json);

	    JFactory::getApplication()->close();
    }
	public function show_msg()
  {
		  $app  = JFactory::getApplication();

		  $msg = $app->input->getVar('msg','123');
	    TopHelpersUtility::show_msg($msg);

	    JFactory::getApplication()->close();
  }

  public function test_ftp()
  {

	    FloorHelpersFloor::test_ftp();

	    echo("OK");
	    JFactory::getApplication()->close();
  }

	public function check_login()
  {

		$app  = JFactory::getApplication();

		$name = $app->input->json->getVar('name','');

		$status = TopHelpersUtility::is_need_logout($name);

    $json = json_encode($status);

		echo($json);
	  JFactory::getApplication()->close();
  }
  public function test_pdf()
	{
		//require_once('/var/www/html/tcpdf/examples/example_008.php');
    require_once('/var/www/html/tcpdf/examples/example.php');

		JFactory::getApplication()->close();
	}

  public function reset_info_msg()
	{
		$app  = JFactory::getApplication();

		$obj = new stdClass;

		$obj->id = $app->input->getInt('id','');

		TopHelpersUtility::reset_info_msg($obj);
	}

	public function get_info_msg()
	{
    $items = TopHelpersUtility::get_info_msg();

		$json = json_encode($items);

		if(count($items))
		{
      //TopHelpersUtility::save_msg($items[0]->message);
			$cmd = '/bin/bash /opt/24dio/reset_info.sh';
			$url = 'http://127.0.0.1/index.php?option=';

			$param = '"com_floor&task=sroots.reset_info_msg&id='.$items[0]->id.'"';;

			$cmd = $cmd." ".$url .' '.$param.' /tmp/reset_info'.$items[0]->id.' /tmp/RESET_INFO'.$items[0]->id. " 3 &";

			system($cmd);
      //echo($cmd);
			//TopHelpersUtility::reset_info_msg($items[0]);
		}

		echo($json);

		JFactory::getApplication()->close();
	}

	public function send_info_msg()
	{
		$app  = JFactory::getApplication();

	  $obj = new stdClass;

		$obj->msg_context = $app->input->getVar('msg_context','');
    $obj->enable = 1;
    TopHelpersUtility::send_info_msg($obj);
		$json = json_encode($obj);

	  echo($json);
		JFactory::getApplication()->close();
	}

	public function send_alarm_msg()
	{
		$app  = JFactory::getApplication();

	  $obj = new stdClass;

		$obj->msg_type = $app->input->getInt('msg_type',0);
		$obj->msg_number = $app->input->getVar('msg_number','');
		$obj->msg_name = $app->input->getVar('msg_name','');
		$obj->msg_context = $app->input->getVar('msg_context','');

    FloorHelpersFloor::send_alarm_msg($obj);
		$json = json_encode($obj);

	  echo($json);
		JFactory::getApplication()->close();
	}

	public function del_alarm()
	{
		$app  = JFactory::getApplication();

	  $obj = new stdClass;

	  $myaccs  = TopHelpersUtility::getMyAccount();

		if($myaccs[0]->del_alarm_enable)
		{
		    $obj->alarmdir = $myaccs[0]->del_alarmdir;
		    $obj->alarm = $myaccs[0]->del_alarm;

				FloorHelpersFloor::do_alarm($obj);

				$json = json_encode($obj);

			  echo($json);

    }
		else
		{
			echo("disable");
		}

		JFactory::getApplication()->close();
	}

	public function limit_alarm()
	{
		$app  = JFactory::getApplication();

	  $obj = new stdClass;

	  $myaccs  = TopHelpersUtility::getMyAccount();

		if($myaccs[0]->limit_alarm_enable)
		{
		    $obj->alarmdir = $myaccs[0]->limit_alarmdir;
		    $obj->alarm = $myaccs[0]->limit_alarm;

				FloorHelpersFloor::do_alarm($obj);

				$json = json_encode($obj);

			  echo($json);

    }
		else
		{
			echo("disable");
		}

		JFactory::getApplication()->close();
	}
	public function out_alarm()
	{
		$app  = JFactory::getApplication();

	  $obj = new stdClass;

	  $myaccs  = TopHelpersUtility::getMyAccount();

		if($myaccs[0]->out_alarm_enable)
		{
		    $obj->alarmdir = $myaccs[0]->out_alarmdir;
		    $obj->alarm = $myaccs[0]->out_alarm;

				FloorHelpersFloor::do_alarm($obj);

				$json = json_encode($obj);

			  echo($json);

    }
		else
		{
			echo("disable");
		}

		JFactory::getApplication()->close();
	}
	public function passwd_alarm()
	{
		$app  = JFactory::getApplication();

	  $obj = new stdClass;

	  $myaccs  = TopHelpersUtility::getMyAccount();

		if($myaccs[0]->passwd_error_alarm_enable)
		{
		    $obj->alarmdir = $myaccs[0]->passwd_error_alarmdir;
		    $obj->alarm = $myaccs[0]->passwd_error_alarm;

				FloorHelpersFloor::do_alarm($obj);

				$json = json_encode($obj);

			  echo($json);

    }
		else
		{
			echo("disable");
		}

		JFactory::getApplication()->close();
	}
	public function username_alarm()
	{
		$app  = JFactory::getApplication();

	  $obj = new stdClass;

	  $myaccs  = TopHelpersUtility::getMyAccount();

		if($myaccs[0]->username_error_alarm_enable)
		{
		    $obj->alarmdir = $myaccs[0]->username_error_alarmdir;
		    $obj->alarm = $myaccs[0]->username_error_alarm;

				FloorHelpersFloor::do_alarm($obj);

				$json = json_encode($obj);

			  echo($json);

    }
		else
		{
			echo("disable");
		}

		JFactory::getApplication()->close();
	}
	public function update_acc()
	{
		$app  = JFactory::getApplication();

	  $obj = new stdClass;

		$obj->enable = $app->input->getInt('enable',0);
		$obj->server = $app->input->getVar('server','');
		$obj->port = $app->input->getInt('port',21);
		$obj->user = $app->input->getVar('user','');
		$obj->passwd = $app->input->getVar('passwd','');
		$obj->sms_username = $app->input->getVar('sms_username','');
		$obj->sms_passwd = $app->input->getVar('sms_passwd','');
		$obj->rec_sec = $app->input->getInt('rec_sec',5);

		$obj->del_alarm_enable = $app->input->getInt('del_alarm_enable',0);
		$obj->del_alarmdir = $app->input->getInt('del_alarmdir',0);
		$obj->del_alarm = $app->input->getInt('del_alarm',0);

		$obj->limit_alarm_enable = $app->input->getInt('limit_alarm_enable',0);
		$obj->limit_alarmdir = $app->input->getInt('limit_alarmdir',0);
		$obj->limit_alarm = $app->input->getInt('limit_alarm',0);

		$obj->out_alarm_enable = $app->input->getInt('out_alarm_enable',0);
		$obj->out_alarmdir = $app->input->getInt('out_alarmdir',0);
		$obj->out_alarm = $app->input->getInt('out_alarm',0);

		$obj->passwd_error_alarm_enable = $app->input->getInt('passwd_error_alarm_enable',0);
		$obj->passwd_error_alarmdir = $app->input->getInt('passwd_error_alarmdir',0);
		$obj->passwd_error_alarm = $app->input->getInt('passwd_error_alarm',0);

		$obj->username_error_alarm_enable = $app->input->getInt('username_error_alarm_enable',0);
		$obj->username_error_alarmdir = $app->input->getInt('username_error_alarmdir',0);
		$obj->username_error_alarm = $app->input->getInt('username_error_alarm',0);
		$obj->elec_alarm = $app->input->getVar('elec_alarm',0);

    TopHelpersUtility::update_acc($obj);
		$json = json_encode($obj);

	  echo($json);
		JFactory::getApplication()->close();
	}

public function test_curl()
{
	$obj = new stdClass;

	$obj->id = 1;
	$obj->product = "雨水過濾泵";
	$obj->state = "過載";

	$json = json_encode($obj);

	$cmd = 'curl -X PUT https://apexx.duckdns.org/control/weema-device/磐岳科技 --header "Content-Type: application/json" --data-ascii'." '".$json."'";
  system($cmd);
	echo($cmd);
	JFactory::getApplication()->close();
}
public function recordMsg()
{
	$app  = JFactory::getApplication();

  $obj = new stdClass;

	$obj->title = $app->input->getVar('title','');
	$obj->name = $app->input->getVar('name','');
	$obj->ipcam = $app->input->getVar('ipcam','');
	$obj->path = $app->input->getVar('path','');
	$obj->item = $app->input->getVar('item','');
	$obj->member = $app->input->getVar('member','');


	FloorHelpersFloor::create_record($obj);

	$json = json_encode($obj);
	echo($json);

	JFactory::getApplication()->close();

}
	public function getDoorCards()
  {
		$items = FloorHelpersFloor::getDoorCards();

		$json = json_encode($items);
		echo($json);

		JFactory::getApplication()->close();

  }

  public function getBaList()
  {
	    $items = FloorHelpersFloor::getUrlList();

	    $json = json_encode($items);
	    echo($json);

	    JFactory::getApplication()->close();

  }

  public function getTimings()
	{
		$items = FloorHelpersFloor::getTimings();

		$json = json_encode($items);
		echo($json);

		JFactory::getApplication()->close();

	}
	public function reload()
	{
		$app  = JFactory::getApplication();

		$str = $app->input->getVar('str','0');
		$ip = $_SERVER['REMOTE_ADDR'];

		if($str == 'ipcam')
		{
			TopHelpersUtility::reload_ipcam($ip);
		}
		else 
		{
			sleep(5);
			FloorHelpersFloor::reload2();
		}
		echo("OK");

		JFactory::getApplication()->close();
	}
	public function getPhones()
	{
		$items = FloorHelpersFloor::getPhones();

		$json = json_encode($items);
		echo($json);

		JFactory::getApplication()->close();
	}
	public function getCCTVDev()
	{
		$items = FloorHelpersFloor::getCCTVDev();

		$json = json_encode($items);
		echo($json);

		JFactory::getApplication()->close();

	}
	public function getIPDirs()
	{
		$items = FloorHelpersFloor::getIPs();

		$json = json_encode($items);
		echo($json);

		JFactory::getApplication()->close();
	}


	public function getConditions()
	{
		$items = FloorHelpersFloor::getConditions();

		$json = json_encode($items);
		echo($json);

		JFactory::getApplication()->close();
	}

	public function getAlarms()
	{
		$items = FloorHelpersFloor::getAlarms();

		$json = json_encode($items);
		echo($json);

		JFactory::getApplication()->close();
	}

	public function get24DIODevice()
	{
		$items = FloorHelpersFloor::get24DIODevice();

		$json = json_encode($items);
		echo($json);

		JFactory::getApplication()->close();
	}

	public function update_ip2()
	{
		   $app  = JFactory::getApplication();

		   $obj = new stdClass;

       $obj->status = $app->input->json->getInt('status',1);
			 $obj->ids = $app->input->json->get('ids', array(), 'array');

       FloorHelpersFloor::update_ip2($obj);

		    $json = json_encode($obj);
        echo($json);

        echo('OK');
 	      JFactory::getApplication()->close();

	}

	public function startRecord()
	{
		   $app  = JFactory::getApplication();

		   $obj = new stdClass;

       $obj->id = $app->input->getInt('id',1);
       $obj->timeout = $app->input->getInt('timeout',6);

		    FloorHelpersFloor::startRecord($obj);

		    $json = json_encode($obj);
        echo($json);

        echo('OK');
 	      JFactory::getApplication()->close();

	}

	public function getRecordFile()
	{
		   $app  = JFactory::getApplication();

		   $obj = new stdClass;

		   $obj->ipcam = $app->input->getVar('ipaddr',"");
			 //$obj->timeout = $app->input->getInt('timeout',"5");
			 $obj->name = $app->input->getVar('name',"");
			 $obj->id = $app->input->getInt('id',0);
			 $obj->type = $app->input->getInt('type',0);

		    FloorHelpersFloor::waitRecord($obj);

		    $json = json_encode($obj);
        echo($json);

        echo('OK');
 	      JFactory::getApplication()->close();

	}
	public function updateAlarm()
	{
		   $app  = JFactory::getApplication();

		   $obj = new stdClass;

		   $obj->alarm = $app->input->getVar('alarm',"");
		   $obj->status = $app->input->getVar('status',"3");

		    FloorHelpersFloor::update_alarm_device_state($obj);

		    $json = json_encode($obj);
        echo($json);

        echo('OK');
 	      JFactory::getApplication()->close();

	}
  public function get_temp_weather_info()
	{
		$json = TopHelpersUtility::get_temp_weather_info();

    echo($json);
	  JFactory::getApplication()->close();

	}
	public function get_weather()
	{
		$json = TopHelpersUtility::get_weather();

    echo($json);
	  JFactory::getApplication()->close();

	}

	public function phone_alarm()
  	{
		$app  = JFactory::getApplication();

		$obj = new stdClass;

		$alarm = $app->input->getVar('alarm',"");
		
		$obj->alarm = strtok($alarm, "=");
		
		if($obj->alarm === false) 
		{
			echo('alarm FAIL');
			JFactory::getApplication()->close();
	
			return;
		}
		$obj->status = strtok("-");
		if($obj->status === false) 
		{
			echo('status FAIL');
			JFactory::getApplication()->close();
	
			return;
		}		
		$obj->number = strtok("-");
		if($obj->number === false) 
		{
			echo('number FAIL');
			JFactory::getApplication()->close();
	
			return;
		}

		$obj->msg = strtok("-");
		if($obj->msg === false) 
		{
			echo('msg FAIL');
			JFactory::getApplication()->close();
	
			return;
		}

    	FloorHelpersFloor::send_alarm($obj);
		$json = json_encode($obj);
    	echo($json);
    	echo('OK');
 	   JFactory::getApplication()->close();

  	}
  public function get_main_info()
  {
	    $json = TopHelpersUtility::get_main_info();

      echo($json);
 	    JFactory::getApplication()->close();

  }

	public function update_elec_all()
	{

	// 	FloorHelpersFloor::update_elec_all();

    // FloorHelpersFloor::ba_update_elec_all();

	// 	JFactory::getApplication()->close();
	}

	public function update_elec_all2()
	{

		// FloorHelpersFloor::update_elec_all();

		// JFactory::getApplication()->close();
	}

	public function update_kwh()
	{
	// 	$app  = JFactory::getApplication();

	// 	$obj = new stdClass;

	// 	$obj->id = $app->input->getInt('id',0);
	// 	$obj->elec_kwh = $app->input->getInt('kwh',"0");
	// 	$obj->elec_kwh_cur = $app->input->getInt('note',"0");

    // $obj->elec_kwh = $obj->elec_kwh/100.0;

    // $obj->elec_kwh_cur = $obj->elec_kwh_cur/100.0;

	// 	FloorHelpersFloor::update_kwh($obj);
	// 	$json = json_encode($obj);
	// 	echo($json);

	// 	JFactory::getApplication()->close();
	}

	public function update_elec2()
	{
		//JFactory::getApplication()->close();
		//return;
		$this->update_electronic_history();
	    $this->update_elec();
		// JFactory::getApplication()->close();
	}
	public function update_electronic_history()
	{
		$app  = JFactory::getApplication();
		$objectToUpdate = new stdClass;
		$objectToUpdate->id = $app->input->getInt('id',0);
		$objectToUpdate->accumulatepower = $app->input->getDouble('kwh',-100) / 100.0;

		// FloorHelpersFloor::update_watermeter($objectToUpdate);
		$now = new DateTime('now',new DateTimeZone('Asia/Taipei'));
		$year = $now->format("Y");
		$month = $now->format("m");
		$date_of_month = $now->format("d");
		$hour_of_day = $now->format("H");
		$minute = $now->format("i");
		FloorHelpersFloor::add_or_update_hourly_electronicmeter_history($objectToUpdate->id, $objectToUpdate->accumulatepower, $year, $month, $date_of_month, $hour_of_day,$minute);
		// if (!FloorHelpersFloor::chceck_if_hourly_watermeter_record_exists($objectToUpdate->id, $year, $month, $date_of_month, $hour_of_day)) {
		// }
		// echo(json_encode($objectToUpdate) . " " . date("Y-m-d H:i:s"));
		// JFactory::getApplication()->close();
		
	}
	public function update_elec()
  {

		  $app  = JFactory::getApplication();

      $obj = new stdClass;

		  $obj->id = $app->input->getInt('id',0);
			$obj->elec_kwh = $app->input->getInt('kwh',"0");
			$obj->elec_kw = $app->input->getInt('kw',"0");
      $obj->elec_v = $app->input->getInt('volage',"0");
      $obj->elec_a = $app->input->getInt('current',"0");
      $obj->elec_f = $app->input->getInt('freq',"0");
      $obj->elec_pf = $app->input->getInt('power_factor',"0");

			$obj->elec_v_bn = $app->input->getInt('V_BN',"0");
			$obj->elec_v_cn = $app->input->getInt('V_CN',"0");
			$obj->elec_a_a = $app->input->getInt('A_A',"0");
			$obj->elec_a_b = $app->input->getInt('A_B',"0");
			$obj->elec_a_c = $app->input->getInt('A_C',"0");
			$obj->elec_kvar = $app->input->getInt('KVAR',"0");
			$obj->acc_elec_kw = $app->input->getInt('elec_kw',"0");
			$obj->version = $app->input->getInt('version',"0");
			$obj->node_type = $app->input->getInt('node_type',"0");
			
			$obj->temperature1 = $app->input->getFloat('temperature1',"0");
			$obj->temperature2 = $app->input->getFloat('temperature2',"0");
			$obj->temperature3 = $app->input->getFloat('temperature3',"0");
			$obj->temperature4 = $app->input->getFloat('temperature4',"0");

			$obj->acc_elec_kw = $obj->acc_elec_kw/100;

			$obj->elec_kwh = $obj->elec_kwh/100;
			$obj->elec_kw = $obj->elec_kw/100;
      $obj->elec_v = $obj->elec_v/100;
      $obj->elec_a = $obj->elec_a/100;
      $obj->elec_f = $obj->elec_f/100;
      $obj->elec_pf = $obj->elec_pf/100;

			$obj->elec_v_bn = $obj->elec_v_bn/100;
			$obj->elec_v_cn = $obj->elec_v_cn/100;
			$obj->elec_a_a = $obj->elec_a_a/100;
			$obj->elec_a_b = $obj->elec_a_b/100;
			$obj->elec_a_c = $obj->elec_a_c/100;
			$obj->elec_kvar = $obj->elec_kvar/100;

      FloorHelpersFloor::update_elec($obj);
	  $myAccount = TopHelpersUtility::getMyAccount()[0];
	  $elec_ids = explode(" ", $myAccount->elec_list);

	  if (in_array($obj->id,$elec_ids))
	  {
		  FloorHelpersFloor::update_acc_elec_kw($obj);
	  }
      $json = json_encode($obj);
      echo($json);

	    JFactory::getApplication()->close();
  }
  public function update_mitsubishielevator()
  {
	  $app  = JFactory::getApplication();
	  $objectToUpdate = new stdClass;
	  $objectToUpdate->id = $app->input->getInt('id',0);
	  $objectToUpdate->floor_display_name = $app->input->get('floor_display_name','NA');
	//   $objectToUpdate->operate_di = $app->input->getInt('operate_di', 1);
	//   $objectToUpdate->malfunction_di = $app->input->getInt('malfunction_di', 0);
	  FloorHelpersFloor::update_mitsubishielevator($objectToUpdate);
	  echo(json_encode($objectToUpdate));
	  JFactory::getApplication()->close();
  }
  public function update_liuchuanelevator()
  {
	  $app  = JFactory::getApplication();
	  $objectToUpdate = new stdClass;
	  $objectToUpdate->id = $app->input->getInt('id',0);
	  $objectToUpdate->floor_display_name = $app->input->get('floor_display_name','NA');
	  $objectToUpdate->operate_di = $app->input->getInt('operate_di', 1);
	  $objectToUpdate->malfunction_di = $app->input->getInt('malfunction_di', 0);
	  FloorHelpersFloor::update_liuchuanelevator($objectToUpdate);
	  echo(json_encode($objectToUpdate));
	  JFactory::getApplication()->close();
  }
	  public function update_cosensor()
	  {
		$app  = JFactory::getApplication();
		$objectToUpdate = new stdClass;
		$objectToUpdate->id = $app->input->getInt('id',0);
		$objectToUpdate->co = $app->input->getInt('co',-100);
		// $objectToUpdate->status = $app->input->get('status','1');
		FloorHelpersFloor::update_cosensor($objectToUpdate);
		echo(json_encode($objectToUpdate));
		JFactory::getApplication()->close();
	  }
	  public function update_analog()
	  {
		$app  = JFactory::getApplication();
		$objectToUpdate = new stdClass;
		$objectToUpdate->id = $app->input->getInt('id',0);
		$objectToUpdate->useUInt = $app->input->getInt('use_uint',0);
		$objectToUpdate->text_value = substr(urldecode($app->input->getVar('text_value','')),0,255);
		if ($objectToUpdate->useUInt == 1)
		{
			try {				
				$decimal_value = urldecode($app->input->getInt('decimal_value',0));
				$objectToUpdate->decimal_value = unpack('d',pack('Q',$decimal_value))[1];
			} catch (\Throwable $th) {
				//throw $th;
				$objectToUpdate->decimal_value = 999999;
				$objectToUpdate->text_value = "php error";
			}			
		}
		else
		{
			$objectToUpdate->decimal_value = $app->input->getVar('decimal_value',0);
		}
		// $objectToUpdate->status = $app->input->get('status','1');
		FloorHelpersFloor::update_analog($objectToUpdate);
		echo(json_encode($objectToUpdate));
		JFactory::getApplication()->close();
	  }
	  public function update_jetec_soil_meter()
	  {
		$app  = JFactory::getApplication();
		$objectToUpdate = new stdClass;
		$objectToUpdate->id = $app->input->getInt('id',0);
		$objectToUpdate->decimal_values = $app->input->get('decimal_values',array(),'array');
		// $objectToUpdate->status = $app->input->get('status','1');
		FloorHelpersFloor::update_jetec_soil_meter($objectToUpdate);
		echo(json_encode($objectToUpdate));
		// echo(json_encode(FloorHelpersFloor::update_jetec_soil_meter($objectToUpdate)));
		JFactory::getApplication()->close();
	  }
	  public function update_watermeter()
	  {
		$app  = JFactory::getApplication();
		$objectToUpdate = new stdClass;
		$objectToUpdate->id = $app->input->getInt('id',0);
		$objectToUpdate->accumulateWaterFlow = $app->input->getFloat('accumulateWaterFlow',-100); 
		$objectToUpdate->instantWaterFlow = $app->input->getFloat('instantWaterFlow',0); 
		FloorHelpersFloor::update_watermeter($objectToUpdate);
		$year = date("Y");
		$month = date("m");
		$date_of_month = date("d");
		$hour_of_day = date("H");

		FloorHelpersFloor::add_or_update_hourly_watermeter_history($objectToUpdate->id, $objectToUpdate->accumulateWaterFlow, $year, $month, $date_of_month, $hour_of_day);
		// if (!FloorHelpersFloor::chceck_if_hourly_watermeter_record_exists($objectToUpdate->id, $year, $month, $date_of_month, $hour_of_day)) {
		// }
		$objectToUpdate->year = $year;
		$objectToUpdate->month = $month;
		$objectToUpdate->date_of_month = $date_of_month;
		$objectToUpdate->hour_of_day = $hour_of_day;
		echo(json_encode($objectToUpdate) . " " . date("Y-m-d H:i:s"));
		JFactory::getApplication()->close();
	  }

	public function update_temp2()
  {
		$this->update_temp();
  }
  	public function update_iaq()
  	{
		$app  = JFactory::getApplication();

		$obj = new stdClass;

		$obj->id = $app->input->getInt('id',0);

		$obj->temperature = $app->input->getFloat('temperature',0);
		$obj->humidity = $app->input->getFloat('humidity',0);
		$obj->lux = $app->input->getInt('lux', 0);
		$obj->co2 = $app->input->getInt('co2', 0);
		$obj->co = $app->input->getInt('co', 0);
		$obj->hcho = $app->input->getInt('hcho', 0);
		$obj->tvoc = $app->input->getInt('tvoc', 0);
		$obj->pm01 = $app->input->getInt('pm01', 0);
		$obj->pm25 = $app->input->getInt('pm25', 0);
		$obj->pm10 = $app->input->getInt('pm10', 0);

		// FloorHelpersFloor::update_temp($obj);
		FloorHelpersFloor::update_iaq($obj);
		$json = json_encode($obj);
		echo($json);

		JFactory::getApplication()->close();
	}
	public function update_temp()
  {
		  $app  = JFactory::getApplication();

      $obj = new stdClass;

		  $obj->id = $app->input->getInt('id',0);
			$obj->temp = $app->input->getFloat('temp',-100);
			$obj->humidity = $app->input->getFloat('humidity',0);
			$obj->lux = $app->input->getInt('lux',0);
			$obj->co2_ppm = $app->input->getInt('co2_ppm', -1);

      FloorHelpersFloor::update_temp($obj);
      $json = json_encode($obj);
      echo($json);

	    JFactory::getApplication()->close();
	}
	
	public function update_smartlamp()
  {
		  $app  = JFactory::getApplication();

      $obj = new stdClass;

		  $obj->id = $app->input->getInt('id',0);
			$obj->voltage = $app->input->getFloat('voltage',0);
			$obj->current = $app->input->getFloat('current',0);
			$obj->power = $app->input->getFloat('power',0);
			$obj->temperature1 = $app->input->getFloat('temperature1',0);
			$obj->temperature2 = $app->input->getFloat('temperature2',0);
			$obj->temperature3 = $app->input->getFloat('temperature3',0);
			$obj->temperature4 = $app->input->getFloat('temperature4',0);
			$obj->status = $app->input->getInt('status',0);
			$obj->accumulatePower = $app->input->getFloat('accumulatePower',0);
			$obj->timestamp = $app->input->getInt('timestamp',0);
			$update_time = new DateTime();
			$update_time->setTimestamp($obj->timestamp);
			$obj->timestamp_string = $update_time->format("Y-m-d H:i:s");
			FloorHelpersFloor::update_smart_lamp($obj);
			// FloorHelpersFloor::add_smart_lamp_log($obj);
      $json = json_encode($obj);
      echo($json);
			// echo $obj->timestamp_string;
	    JFactory::getApplication()->close();
  }
	public function doPhoneCCTV($items1,$obj)
	{
	    if($obj->type == "call_start")
		  {
			    $items1[0]->open = 1;
					;//system('/bin/bash /opt/24dio/cctv_timeout.sh '.$items1[0]->id." ".$obj->cctv_timeout." > /dev/null 2> /dev/null &");

		  }
		  else if($obj->type == "call_established")
		  {
				  $items1[0]->status = 2;
		  }
		  else
		  {
			    if($obj->type == "call_ended")
			    {
					    if($items1[0]->status == 2)
					    {
						      $items1[0]->status = 1;
					    }
			    }

			    $items1[0]->open = 0;
		  }

		  //$items1[0]->status = $status;
		  $items1[0]->path = $obj->path;
		  $items1[0]->caller = $obj->caller;
		  $items1[0]->callee = $obj->callee;

		  FloorHelpersFloor::update_device_state($items1[0]);

		  if($obj->type == "call_start")
		  {
				   $items1[0]->info = $obj->caller." CALL ".$obj->callee;
				   FloorHelpersFloor::phoneCCTVToLog($items1[0]);
		  }
		  else if($obj->type == "call_ended" || $obj->type == "call_fail")
		  {
			    $items1[0]->status = 1;
			    $mylog = FloorHelpersFloor::findDevicelog($items1[0],1);
			    if(count($mylog))
					    FloorHelpersFloor::update_devicelog($mylog[0],$items1[0]);

		  }

	}
	public function update_sip()
	{
		$app  = JFactory::getApplication();

    $obj = new stdClass;

		$obj->status = $app->input->json->getVar('status',"");

    $obj->numbers = $app->input->json->get('number', array(), 'array');

    FloorHelpersFloor::save_device_status2($obj);

		$json = json_encode($obj);
		echo($json);
		JFactory::getApplication()->close();

	}
	public function getRelayEvent()
	{
		$app  = JFactory::getApplication();

    $obj = new stdClass;
		$obj->type = $app->input->json->getVar('event_type',"");

		$caller = $app->input->json->getVar('caller',"");
	  $obj->caller = FloorHelpersFloor::getSIPnumber($caller);

		$callee = $app->input->json->getVar('callee',"");

		$obj->callee = FloorHelpersFloor::getSIPnumber($callee);
		$obj->time = $app->input->json->get('time',"");

    $json = json_encode($obj);		
    echo($json);
		FloorHelpersFloor::write_to_socket2('SIP='.$json);
		if ($obj->type == 'call_start') //call_ended
		{
			$obj1 = new stdClass();
			// $obj->number = str_replace("sip:","", strtok($obj->caller,'@'));
			$obj1->number = $obj->caller;
			$obj1->status = '3';
			$obj1->msg = '100';
			FloorHelpersFloor::send_alarm($obj1, false);
		} else if ($obj->type == 'call_ended' || $obj->type == 'call_fail')
		{
			$obj1 = new stdClass();
			// $obj->number = str_replace("sip:","", strtok($obj->caller,'@'));
			$obj1->number = $obj->caller;
			$obj1->status = '1';
			$obj1->msg = '100';
			FloorHelpersFloor::send_alarm($obj1, false);
		}
		JFactory::getApplication()->close();

	}
	public function doGetMyEvent()
	{
		$app  = JFactory::getApplication();

    $obj = new stdClass;

		$obj->event_type = $app->input->json->getVar('event_type',"");

		$obj->caller = $app->input->json->getVar('caller',"");
		$obj->callee = $app->input->json->getVar('callee',"");
		$obj->time = $app->input->json->get('time',"");

    $json = json_encode($obj);

    FloorHelpersFloor::MyEventRelayAll($json);

		JFactory::getApplication()->close();

	}

	public function getMyEvent()
  {
		$app  = JFactory::getApplication();

		$type = $app->input->json->getVar('event_type',"");

		$caller = $app->input->json->getVar('caller',"");
		$caller = FloorHelpersFloor::getSIPnumber($caller);
		$callee = $app->input->json->getVar('callee',"");
		$callee = FloorHelpersFloor::getSIPnumber($callee);
		$time = $app->input->json->get('time',"");

    //system("echo ".$caller ." ".$callee . " >> /tmp/myevent");
    $obj = new stdClass;
		$obj->type = $type;
		$obj->caller = $caller;
		$obj->callee = $callee;

		if($type == "call_start" || $type == "call_ended" || $type == "call_fail" || $type == "call_established")
		{
			$items = FloorHelpersFloor::getTopPhoneCCTVDevices($obj);

	    if(count($items))
			{
				  $obj->path = $items[0]->path;
					$obj->cctv_timeout = $items[0]->cctv_timeout;

				  $is_found = false;
          $items1 = FloorHelpersFloor::getPhoneDevices($caller);
					if(count($items1))
					{
						 $is_found = true;
						 $this->doPhoneCCTV($items1,$obj);
          }

					$items1 = FloorHelpersFloor::getPhoneDevices($callee);
					if(count($items1))
					{
						  if($is_found == false)
							{
								$this->doPhoneCCTV($items1,$obj);

							}
							else
							{
							    if($obj->type == "call_ended")
								  {
										   if($items1[0]->status == 2)
										   {
											     $items1[0]->status = 1;
											     FloorHelpersFloor::update_device_state($items1[0]);

										  }
								  }
								  else if($obj->type == "call_established")
								  {
									    $items1[0]->status = 2;
									    FloorHelpersFloor::update_device_state($items1[0]);

								  }
						  }

          }

			}
			else
			{
				$items1 = FloorHelpersFloor::getPhoneDevices($caller);
				if(count($items1))
				{
					 if($type == "call_ended" || $type == "call_fail")
					 {
							 if($items1[0]->status == 2)
							 {
								 $items1[0]->status = 1;
								 FloorHelpersFloor::update_device_state($items1[0]);

							 }

							 $items1[0]->status = 1;
		 			     $mylog = FloorHelpersFloor::findDevicelog($items1[0],1);
		 			     if(count($mylog))
							 {
		 					      FloorHelpersFloor::update_devicelog($mylog[0],$items1[0]);
               }

					 }
					 else if($type == "call_established")
					 {
						 $items1[0]->status = 2;
						 FloorHelpersFloor::update_device_state($items1[0]);

					 }

				}

				$items1 = FloorHelpersFloor::getPhoneDevices($callee);
				if(count($items1))
				{
						if($type == "call_established")
						{
								$items1[0]->status = 2;
						}
						else
						{
							if($type == "call_ended")
							{
									if($items1[0]->status == 2)
									{
										$items1[0]->status = 1;
									}
							}

						}

						//$items1[0]->status = $status;
						$items1[0]->path = $items[0]->path;
						$items1[0]->caller = $caller;
						$items1[0]->callee = $callee;

						FloorHelpersFloor::update_device_state($items1[0]);

				}

			}

		}

		JFactory::getApplication()->close();
  }
	public function cctv_timeout()
  {
		$app  = JFactory::getApplication();

		$obj = new stdClass;

		$ip = $app->input->server->get('REMOTE_ADDR');

		if(FloorHelpersFloor::is_localip($ip) == false)
		{
			JFactory::getApplication()->close();
			return;
		}
		$obj->id = $app->input->getInt('value', 0);

		//echo($obj->status . "<br>".$obj->ipaddr. "<br>");
		FloorHelpersFloor::reset_cctv($obj->id);

		//$json = json_encode($arr);
		echo("cctv timeout");

		//echo('OK');

		JFactory::getApplication()->close();
  }
  public function alarm_timeout()
  {
		$app  = JFactory::getApplication();

		$obj = new stdClass;

		$ip = $app->input->server->get('REMOTE_ADDR');

		if(FloorHelpersFloor::is_localip($ip) == false)
		{
			JFactory::getApplication()->close();
			return;
		}
		$obj->group = $app->input->getInt('value', 0);

		//echo($obj->status . "<br>".$obj->ipaddr. "<br>");
		FloorHelpersFloor::alarm_timeout($obj);

		//$json = json_encode($arr);
		echo("alarm timeout");

		//echo('OK');

		JFactory::getApplication()->close();
  }
	public function dio_condition()
  {
		$app  = JFactory::getApplication();

    $obj = new stdClass;

    $ip = $app->input->server->get('REMOTE_ADDR');

    if(FloorHelpersFloor::is_localip($ip) == false)
		{
			JFactory::getApplication()->close();
			return;
		}
		$obj->value = $app->input->getInt('is_alarm', 0);
		$obj->ipaddr = $app->input->getVar('ipaddr', '');
		$obj->index = $app->input->getInt('index', 0);

    //echo($obj->status . "<br>".$obj->ipaddr. "<br>");
    FloorHelpersFloor::dio_condition($obj);

	  //$json = json_encode($arr);
    echo("OK");

    //echo('OK');

		JFactory::getApplication()->close();
  }
	public function dio_condition2()
  {
		$app  = JFactory::getApplication();

    $obj = new stdClass;

		$obj->value = $app->input->getInt('is_alarm', 0);
		$obj->id = $app->input->getVar('ipaddr', '');
		$obj->index = $app->input->getInt('index', 0);

    //echo($obj->status . "<br>".$obj->ipaddr. "<br>");
    FloorHelpersFloor::dio_condition2($obj);

	  //$json = json_encode($arr);
    echo("OK");

    //echo('OK');

		JFactory::getApplication()->close();
  }
	public function cond_alarm2()
	{

		$app  = JFactory::getApplication();

		$value = $app->input->getInt('value', 0);
		$index = $app->input->getInt('index', 0);
		$ipaddr = $app->input->getVar('ipaddr', '');

		//echo($obj->status . "<br>".$obj->ipaddr. "<br>");
		//FloorHelpersFloor::reset_dio_id();
    $obj = new stdClass;

		$obj->group = $value;
		$obj->status = 3;
		$obj->id = $ipaddr;
		$obj->index = $index;

    FloorHelpersFloor::setCondDIOStatus2($obj);

		echo('cond alarm');

		JFactory::getApplication()->close();

	}
	public function cond_alarm()
	{

		$app  = JFactory::getApplication();

		$ip = $app->input->server->get('REMOTE_ADDR');

    if(FloorHelpersFloor::is_localip($ip) == false)
		{
			JFactory::getApplication()->close();
			return;
		}

		$value = $app->input->getInt('value', 0);
		$index = $app->input->getInt('index', 0);
		$ipaddr = $app->input->getVar('ipaddr', '');

		//echo($obj->status . "<br>".$obj->ipaddr. "<br>");
		//FloorHelpersFloor::reset_dio_id();
    $obj = new stdClass;

		$obj->group = $value;
		$obj->status = 3;
		$obj->ipaddr = $ipaddr;
		$obj->index = $index;

    FloorHelpersFloor::setCondDIOStatus($obj);

		echo('cond alarm');

		JFactory::getApplication()->close();

	}
	public function timing()
	{
		$app  = JFactory::getApplication();

		$ip = $app->input->server->get('REMOTE_ADDR');
		echo($ip);
    if(FloorHelpersFloor::is_localip($ip) == false)
		{
			JFactory::getApplication()->close();
			return;
		}

		$dir = $app->input->getInt('dir', '0');
		$value = $app->input->getInt('value', '0');

		//echo($obj->status . "<br>".$obj->ipaddr. "<br>");
		//FloorHelpersFloor::reset_dio_id();
    $obj = new stdClass;

		$obj->alarmdir = $dir;
		$obj->alarm = $value;
		$obj->status = 0;
		
    FloorHelpersFloor::send_timing_action($obj);

		echo('timing '.$obj->alarmdir.' '.$obj->alarm);

		JFactory::getApplication()->close();

	}

	public function timing2()
	{

		$app  = JFactory::getApplication();

		$value = $app->input->getInt('value', '0');

		//echo($obj->status . "<br>".$obj->ipaddr. "<br>");
		//FloorHelpersFloor::reset_dio_id();
    $obj = new stdClass;

		$obj->group = $value;
		$obj->status = 0;
    FloorHelpersFloor::send_timing_action2($obj);

		echo('timing');

		JFactory::getApplication()->close();

	}

	public function reset_dio_id()
	{
		//echo($obj->status . "<br>".$obj->ipaddr. "<br>");
		FloorHelpersFloor::reset_dio_id();


		echo('OK');

		JFactory::getApplication()->close();

	}

	public function getDIOResult()
	{

		$app  = JFactory::getApplication();

		$ipaddr = $app->input->json->getVar('id', '');

		$items = FloorHelpersFloor::getDIOResult($ipaddr);

    if(count($items))
		{
			  if($items[0]->type == 0)
				{
					$ret = '0';
				}
				else
				{
					$ret = '1';
				}
		}
		else
		{
			$ret = '0';
		}

		echo($ret);

		JFactory::getApplication()->close();

	}



public function check_ip()
{

	$app  = JFactory::getApplication();

	$ipaddr = $app->input->getVar('ipaddr', '');

	//echo($obj->status . "<br>".$obj->ipaddr. "<br>");
	$items = FloorHelpersFloor::getIPDevicesByIpaddr($ipaddr);

  if(count($items))
	{
		//echo('3');
		echo($items[0]->status);
	}
	else
	//$json = json_encode($arr);
	    echo("1");

	//echo('OK');

	JFactory::getApplication()->close();

}

	public function update_do2()
	{

		$app  = JFactory::getApplication();

    $obj = new stdClass;

		$obj->value = $app->input->getInt('value', 0);
		$obj->ipaddr = $app->input->getVar('ipaddr', '');
		$obj->index = $app->input->getInt('index', 0);

    //echo($obj->status . "<br>".$obj->ipaddr. "<br>");
    FloorHelpersFloor::updateDO2($obj);

	  //$json = json_encode($arr);
    echo("OK");

    //echo('OK');

		JFactory::getApplication()->close();

	}
	public function update_do()
	{

		$app  = JFactory::getApplication();

		$ip = $app->input->server->get('REMOTE_ADDR');

		if(FloorHelpersFloor::is_localip($ip) == false)
		{
			JFactory::getApplication()->close();
			return;
		}

		$obj = new stdClass;

		$obj->value = $app->input->getInt('value', 0);
		$obj->ipaddr = $app->input->getVar('ipaddr', '');
		$obj->index = $app->input->getInt('index', 0);

		//echo($obj->status . "<br>".$obj->ipaddr. "<br>");
		FloorHelpersFloor::updateDO($obj);

		//$json = json_encode($arr);
		echo("OK");

		//echo('OK');

		JFactory::getApplication()->close();

	}
	public function update_soyal()
	{

		$app  = JFactory::getApplication();

		$ip = $app->input->server->get('REMOTE_ADDR');

    if(FloorHelpersFloor::is_localip($ip) == false)
		{
			JFactory::getApplication()->close();
			return;
		}

    $obj = new stdClass;

		$obj->value = $app->input->getInt('state', 0);
		$obj->id = $app->input->getVar('id', '');

    //echo($obj->status . "<br>".$obj->ipaddr. "<br>");
    FloorHelpersFloor::updateSOYAL($obj);

	  //$json = json_encode($arr);
    echo("OK");

    //echo('OK');

		JFactory::getApplication()->close();

	}

	public function update_di2()
	{

		$app  = JFactory::getApplication();

    $obj = new stdClass;

		$obj->value = $app->input->getInt('value', 0);
		$obj->ipaddr = $app->input->getVar('ipaddr', '');
		$obj->index = $app->input->getInt('index', 0);

    //echo($obj->status . "<br>".$obj->ipaddr. "<br>");
    FloorHelpersFloor::updateDI($obj);

	  //$json = json_encode($arr);
    echo("OK");

    //echo('OK');

		JFactory::getApplication()->close();

	}
	public function update_di()
	{

		$app  = JFactory::getApplication();

		$ip = $app->input->server->get('REMOTE_ADDR');

    if(FloorHelpersFloor::is_localip($ip) == false)
		{
			JFactory::getApplication()->close();
			return;
		}

    $obj = new stdClass;

		$obj->value = $app->input->getInt('value', 0);
		$obj->ipaddr = $app->input->getVar('ipaddr', '');
		$obj->index = $app->input->getInt('index', 0);

    //echo($obj->status . "<br>".$obj->ipaddr. "<br>");
    FloorHelpersFloor::updateDI($obj);

	  //$json = json_encode($arr);
    echo("OK");

    //echo('OK');

		JFactory::getApplication()->close();

	}
	public function do_call()
	{

		$app  = JFactory::getApplication();

    $obj = new stdClass;

		$obj->caller = $app->input->json->getInt('caller', "");
		$obj->callee = $app->input->json->getVar('callee', '');
		$obj->check_name = $app->input->json->getVar('check_name', '');

    //echo($obj->status . "<br>".$obj->ipaddr. "<br>");
    FloorHelpersFloor::setCallInfo($obj);

	  //$json = json_encode($arr);
    echo("OK");

    //echo('OK');

		JFactory::getApplication()->close();

	}
	public function do_action()
	{

		$app  = JFactory::getApplication();

    $obj = new stdClass;

		$obj->id = $app->input->json->getInt('id', 0);
		$obj->check_name = $app->input->json->getVar('name', '');

    //echo($obj->status . "<br>".$obj->ipaddr. "<br>");
    FloorHelpersFloor::setDOAction1($obj);

	  //$json = json_encode($arr);
    echo("OK");

    //echo('OK');

		JFactory::getApplication()->close();

	}



public function test_utility()
{

	$myacc  = FloorHelpersFloor::test_utility();

	$json = json_encode($myacc);
	echo($json);

	//echo('OK');

	JFactory::getApplication()->close();

}

public function getMyAccount()
{

	$myacc  = TopHelpersUtility::getMyAccount();

	$json = json_encode($myacc);
	echo($json);

	//echo('OK');

	JFactory::getApplication()->close();

}

	public function getCallee()
	{

		$callees  = FloorHelpersFloor::getCallee();

	  $json = json_encode($callees);
		echo($json);

    //echo('OK');

		JFactory::getApplication()->close();

	}

	public function do_action2()
	{

		$app  = JFactory::getApplication();

    $obj = new stdClass;

    $obj->parent_id = $app->input->getInt('parent', 0);
		$obj->dio_id = $app->input->getInt('dio_id', 0);
		$obj->index = $app->input->getInt('index', 0);
    $obj->value = $app->input->getInt('value', 0);
		$obj->timeout = $app->input->getInt('timeout', 0);
    //echo($obj->status . "<br>".$obj->ipaddr. "<br>");
    FloorHelpersFloor::setDOAction2($obj);

	  $json = json_encode($obj);
		echo($json);
    echo("OK");

    //echo('OK');

		JFactory::getApplication()->close();

	}
	public function test_history1()
	{
		$app  = JFactory::getApplication();
		$range_type = $app->input->getInt('range_type', -1);
		$device_id = $app->input->getInt('device_id', -1);
		$year = $app->input->getInt('year', -1);
		$month = $app->input->getInt('month', -1);
		$date_of_month = $app->input->getInt('date_of_month', -1);
		$hour_of_day = $app->input->getInt('hour_of_day', -1);
		$items = new StdClass();
		$items->annual =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,-1,-1,-1,-1);
		$items->year =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,-1,-1,-1);
		$items->month =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,$month,-1,-1);
		$items->date_of_month =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,$month,$date_of_month,-1);
		$items->hour_of_day =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,$month,$date_of_month,-1);
		echo($items);
		$app->close();
	}
	public function test_history()
	{
		$app = JFactory::getApplication();
		$device_id = $app->input->getInt('device_id', 10);
		$year = $app->input->getInt('year',-1);
		$month = $app->input->getInt('month', -1);
		$date_of_month = $app->input->getInt('date_of_month', -1);
		$hour_of_day = $app->input->getInt('hour_of_day', -1);
		$summer = new StdClass();
		$summer->peak = 2.32;
		$summer->mid = 1.42;
		$summer->off_peak = 0.91;
		$not_summer = new StdClass();
		$not_summer->peak = 2.24;
		$not_summer->mid = 1.35;
		$not_summer->off_peak = 0.84;

		$pricing = new StdClass();
		$pricing->summer = $summer;
		$pricing->not_summer = $not_summer;
		// $items = Electronic_meter_historyHelpersElectronic_meter_history::get_hourly_electronic_meter_statistics_by_period($device_id, $year, $month, $date_of_month);
		// $items = Electronic_meter_historyHelpersElectronic_meter_history::get_daily_electronic_meter_statistics($device_id, $year, $month);
		$items = new StdClass();
		$items->annual =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,-1,-1,-1,-1);
		$items->year =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,-1,-1,-1);
		$items->month =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,$month,-1,-1);
		$items->date_of_month =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,$month,$date_of_month,-1);
		$items->hour_of_day =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,$month,$date_of_month,-1);
		echo($items->date_of_month); 
		$app->close();
		// Electronic_meter_historyHelpersElectronic_meter_history::get_hourly_electronic_meter_statistics($device_id, $year, $month, $date_of_month);
	}
	// public function HumanSize($Bytes)
	// {
	// 	$Type=array("bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB");
	// 	$Index=0;
	// 	while($Bytes>=1024)
	// 	{
	// 		$Bytes/=1024;
	// 		$Index++;
	// 	}
	// 	$Bytes = round($Bytes,2);
	// 	return("".$Bytes." ".$Type[$Index]);
	// }
	public function test_status()
	{
		$app = JFactory::getApplication();

		// $devs = FloorHelpersFloor::getAnalogDevices();
		$db = JFactory::getDbo();

		$query = $db->getQuery(true);
		$query->from('`#__device_table`')
				->select("GROUP_CONCAT(replace(info,'C$','')) as tids")
				->where('dio_type =999')
				->where('enable = 1')
				->where("info like '%C$%'");
				
		$db->setQuery($query);
		$fake_dios = $db->loadObject();
		echo(json_encode($fake_dios));
		$app->close();
	}
	public function get_temperature_humidity()
	{
		$app  = JFactory::getApplication();
		$temperature_device_id = $app->input->getInt('temperature_device_id', 0);
		$humidity_device_id = $app->input->getInt('humidity_device_id', 0);

		$db = JFactory::getDbo();

		$query = $db->getQuery(true);
		$query->from('`#__device_table`')
				->select("id,temp")				
				->where('enable = 1')
				->where("id = ".$temperature_device_id);
				
		$db->setQuery($query);
		$result = $db->loadObject();	

		$r = new stdClass();
		$r->temperature = floatval($result->temp);

		$query = $db->getQuery(true);
		$query->from('`#__device_table`')
				->select("id,humidity")				
				->where('enable = 1')
				->where("id = ".$humidity_device_id);
				
		$db->setQuery($query);
		$result = $db->loadObject();
		$r->humidity = intval($result->humidity);
		// $r->result = $result;
		echo(json_encode($r));
		$app->close();
	}
	public function test_api()
	{
		$app  = JFactory::getApplication();
		$id = $app->input->getInt('device_id', 10);
		$t = new DateTime('now',new DateTimeZone('Asia/Taipei'));
		$app  = JFactory::getApplication();
		$a = new stdClass();
		$lastRecord = Water_meter_historyHelpersWater_meter_history::get_last_hourly_water_meter_record(123,2023,9,27,15);
		if (count($lastRecord) > 0)
		{
			$a->lastAccum = $lastRecord[0]->accumulatewaterflow;
		}
		$a->lastRecord = $lastRecord;
		// $a->list = FloorHelpersFloor::getDioSupressedAlarmNodes();
		// $a->time = $t->format('Hi');
		
		// $a->status = FloorHelpersFloor::getDioTriggerAlarmStatus($id);
		// $a->id = $id;
		// $a->findMaxLogId = FloorHelpersFloor::findMaxLogId1();
		// $a->is_need_alarm = FloorHelpersFloor::is_need_alarm();
		// $mute_nodes = FloorHelpersFloor::getDioSupressedAlarmNodes();
		// $a->allFloorAlarmCount = FloorHelpersFloor::getAllFloorAlarmCount($mute_nodes);
		// $a->test_meter_comb = FloorHelpersFloor::getAllEnableDeviceDataByFloor(64);
		// $build_alarm_counts = FloorHelpersFloor::getAllBuildingAlarmCount($mute_nodes);
		// $a->allBuildingAlarmCount = $build_alarm_counts;

		// $menus = FloorHelpersFloor::getAllMenuAlarmCount();
		// foreach ($menus as $key => $menu) {
		// 	if (count($menu->buildings) > 0)
		// 	{
		// 		$menus[$key]->count = 0;				
		// 		foreach ($build_alarm_counts as $key2 => $alarm_count) {
		// 			foreach ($menu->buildings as $key1 => $building) {
		// 				if ($menu->reversed)
		// 				{
		// 					if ($building != $alarm_count->building)
		// 					{							
		// 						$menus[$key]->count += intval($alarm_count->alarm_count);	
		// 					}
		// 				}
		// 				else
		// 				{
		// 					if ($building == $alarm_count->building)
		// 					{							
		// 						$menus[$key]->count += intval($alarm_count->alarm_count);	
		// 					}
		// 				}
		// 			}
		// 		}
		// 		// unset($menus[$key]->buildings);
		// 	}
		// }
		// // $a->allMenuAlarmCount = array_filter($menus,function($menu,$key) {
		// // 	return $menu->count > 0;
		// // },ARRAY_FILTER_USE_BOTH);
		// $a->allMenuAlarmCount = $menus;
		// $disk_space = disk_free_space(".");
		// $disk_space = FloorHelpersFloor::getDiskSpace();
		// $db_size = FloorHelpersFloor::getDbSize();
		// $a->disk_size = FloorHelpersFloor::HumanSize($disk_space);
		// $a->db_size = FloorHelpersFloor::HumanSize($db_size);
		// $a->usage = round($db_size/$disk_space,2)." %";		

		// // $a->test = $menus;
		echo(json_encode($a));
		
		
		$app->close();
	}
	public function test()
	{
		if (!defined('_JEXEC'))
		{
			// Initialize Joomla framework
			define('_JEXEC', 1);
		}

		// Load system defines
		if (file_exists(dirname(__FILE__) . '/defines.php'))
		{
			require_once dirname(__FILE__) . '/defines.php';
		}
		if (!defined('JPATH_BASE'))
		{
			define('JPATH_BASE', dirname(__FILE__));
		}
		if (!defined('_JDEFINES'))
		{
			require_once JPATH_BASE . '/includes/defines.php';
		}

		// Get the framework.
		require_once JPATH_BASE . '/includes/framework.php';

		// $app = JFactory::getApplication('site');
		// $app->initialise();
		$app  = JFactory::getApplication();
		$app->initialise();
		$user = JFactory::getUser();
		// echo (json_encode($user));
		echo (json_encode(FloorHelpersFloor::getIfUserSuppressedAutoRedirect($user)));
		JFactory::getApplication()->close();
	}
	public function update_device_status()
	{
		$app  = JFactory::getApplication();

		$id = $app->input->getInt('id', 0);
		$status = $app->input->getInt('status', 0);
		$message = $app->input->getVar('message', '');

		//echo($obj->status . "<br>".$obj->ipaddr. "<br>");
		FloorHelpersFloor::update_device_status($id, $status);
		// echo $id;
		// echo $status;
		// echo $message;
		//$json = json_encode($arr);
		echo("OK");

		//echo('OK');

		JFactory::getApplication()->close();
	}
	public function dio_alarm2()
	{
		$app  = JFactory::getApplication();

		$obj = new stdClass;

		$obj->status = $app->input->getInt('is_alarm', 0);
		$obj->id = $app->input->getVar('ipaddr', '');
		$obj->index = $app->input->getInt('index', 0);

		//echo($obj->status . "<br>".$obj->ipaddr. "<br>");
		FloorHelpersFloor::setDIOStatus2($obj);
		JLog::add("dio_alarm2", JLog::INFO, 'jerror');
		//$json = json_encode($arr);
		echo("OK");

		//echo('OK');

		JFactory::getApplication()->close();

	}
	public function dio_alarm()
	{
		$app  = JFactory::getApplication();

		$ip = $app->input->server->get('REMOTE_ADDR');

    if(FloorHelpersFloor::is_localip($ip) == false)
		{
			JFactory::getApplication()->close();
			return;
		}

    $obj = new stdClass;

		$obj->status = $app->input->getInt('is_alarm', 0);
		$obj->ipaddr = $app->input->getVar('ipaddr', '');
		$obj->index = $app->input->getInt('index', 0);

    //echo($obj->status . "<br>".$obj->ipaddr. "<br>");
    FloorHelpersFloor::setDIOStatus($obj);

	  //$json = json_encode($arr);
    echo("OK");

    //echo('OK');

		JFactory::getApplication()->close();

	}

	public function ping_online()
	{
		FloorHelpersFloor::one_second_pass();
	  $arr = FloorHelpersFloor::getIPStatus(1);

	  $json = json_encode($arr);
    echo($json);

    //echo('OK');

		JFactory::getApplication()->close();

	}

	public function ping_offline()
	{
	  $arr = FloorHelpersFloor::getIPStatus(3);

	  $json = json_encode($arr);
    echo($json);

		JFactory::getApplication()->close();

	}

	public function sendMsg()
  {

		   $obj = new stdClass;
			 $obj->desc = 'alarm';
	     FloorHelpersFloor::sendMessage($obj);

			 JFactory::getApplication()->close();
  }

	public function getSIPStatus()
  {

		   $myfile = fopen("/opt/PHONEFILEBAK", "r") or die("Unable to open file!");
		   $read = fread($myfile,filesize("/opt/PHONEFILEBAK"));
		   fclose($myfile);


       $phone =  json_decode($read,true);

			$item = $phone[0];

	 	  $arr["name"] = $item["username"];
	 		$arr["password"] = $item["password"];

	 	  $json = json_encode($arr);

	 		$addr = "http://".$item["serverip"] .":8899/api/account/credentials/verify";
	    $file = "/var/www/html/POST";

	 		$cmd = 'wget -T 2 -O ' . $file . " --post-data='" . $json . "' " . $addr;

	 		system($cmd);
	    //echo($cmd."<br>");

	 		$myFile = "/var/www/html/POST";

	 	  $content = JFile::read($myFile);

	 	  if(!empty($content))
	     {
	 				$myjson = json_decode($content,true);

	         $this->updatesip_device_state($item["Phones"],$item["serverip"],$myjson["access_token"]);
	         //echo("OK");
	 		}
	 		else
	 		{
	 			 //echo('error');
	 		}
	 		JFactory::getApplication()->close();
  }

	public function update_sip_status()
	{
		$this->getToken();
	}
	public function getToken($is_phoneCCTV = 0)
	{
		$items = FloorHelpersFloor::getSIPINFO();

		if(count($items) <= 0)
		{
			echo("no sip info");
			JFactory::getApplication()->close();
      return;
		}

	  $arr["name"] = $items[0]->username;
		$arr["password"] = $items[0]->password;

	  $json = json_encode($arr);

		$addr = "http://".$items[0]->serverip .":8899/api/account/credentials/verify";
    $file = "/var/www/html/POST";

		$cmd = 'wget -T 2  -O ' . $file . " --post-data='" . $json . "' " . $addr;

		system($cmd);
    echo($cmd."<br>");

		$myFile = "/var/www/html/POST";

	  $content = JFile::read($myFile);

	  if(!empty($content))
    {
				$myjson = json_decode($content,true);

        $this->update_device_state($items[0]->serverip,$myjson["access_token"],$is_phoneCCTV);
        echo("OK");
		}
		else
		{
			 echo('error');
		}
		JFactory::getApplication()->close();

	}

	public function getDevice()
	{
		$app  = JFactory::getApplication();

		$id = $app->input->json->getInt('id',0);

	  $arr = FloorHelpersFloor::getDevice($id);
		if(count($arr))
		{
			  $floors = FloorHelpersFloor::get_my_floor($arr[0]->floor);
				if(count($floors))
				{
					$buildings = FloorHelpersFloor::getBuildingItem($floors[0]->building);
          $arr[0]->building = $buildings[0]->id;
				}

    }
	  $json = json_encode($arr);

    echo($json);

		JFactory::getApplication()->close();

	}
	public function getStatus()
	{
	  $arr = FloorHelpersFloor::getDevicesStatus();

	  $json = json_encode($arr);

    echo($json);

		JFactory::getApplication()->close();

	}

	public function getPhoneCCTVStatus()
	{
	  $arr = FloorHelpersFloor::getPhoneCCTVStatus();

	  $json = json_encode($arr);

    echo($json);

		JFactory::getApplication()->close();

	}

	public function update_device_state($serverip,$mytoken,$is_phoneCCTV = 0)
	{

		$items = FloorHelpersFloor::getSIPDevices();

    //echo("update_device_state");
    //return;
		$arr["access_token"] = $mytoken;

		$num_arr = array();
		foreach($items as $i => $item)
		{
				if($item->info != "")
			      array_push($num_arr, $item->info);

        if(count($num_arr) >= 100)
				{
					  //echo("num 2<br>");
            $this->do_update_device_state($serverip,$mytoken,$num_arr,$items,$arr);
						$num_arr = array();
				}
		}

		if(count($num_arr) > 0)
		{
				$this->do_update_device_state($serverip,$mytoken,$num_arr,$items,$arr);

		}

	}

	public function updatesip_device_state($items,$serverip,$mytoken,$is_phoneCCTV = 0)
	{

		$arr["access_token"] = $mytoken;

    $sip = new stdClass;

		$num_arr = array();
		foreach($items as $i => $item)
		{
				if($item["info"] != "")
			      array_push($num_arr, $item["info"]);

        if(count($num_arr) >= 5)
				{
					  //echo("num 2<br>");
            $this->do_updatesip_device_state($serverip,$mytoken,$num_arr,$arr,$sip);
						$num_arr = array();


				}
		}

		if(count($num_arr) > 0)
		{
			  //echo("num 1<br>");
				$this->do_updatesip_device_state($serverip,$mytoken,$num_arr,$arr,$sip);

		}

    $json = json_encode($sip->content);
		echo($json);
	}

  public function getUser1()
	{
				$user = JUser::getInstance('mykevin');
				$pass = '123456';
				$username = 'mykevin';
				$password = array('password' => $pass, 'password2' => $pass,'username'=>$username);
			   //$user->bind($password);
			   //$user->save();
				 //echo(md5($user->password));
				 //echo('<br>');
         //echo(md5('123456'));

				 //$user = JFactory::getUser(); // or: getUser($id) to get the user with ID $id
         $entered_password = '1234569';
				 $passwordMatch = JUserHelper::verifyPassword($entered_password, $user->password, $user->id);

         if($passwordMatch == true)
             echo($passwordMatch);
				 else
				     echo('FAIL');
				JFactory::getApplication()->close();

	}
	public function getPath()
	{
		$app  = JFactory::getApplication();

		$obj = new stdClass;

		$obj->id = $app->input->json->get('id', array(), 'array');

    FloorHelpersFloor::getDevicePath($obj);

		$json = json_encode($obj->arr);

		echo($json);
      	JFactory::getApplication()->close();
	}
	public function getFloorImageUrl()
	{
		$app = JFactory::getApplication();		
		$id = $app->input->getInt('id');
		$path = FloorHelpersFloor::getFloorImagePath($id);
		// return $path;
		echo($path);
		// echo("");
		// echo new JsonResponse($result);
		JFactory::getApplication()->close();
	}
	public function getDeviceImageUrl()
	{
		$app = JFactory::getApplication();		
		$id = $app->input->getInt('id');
		$path = FloorHelpersFloor::getDeviceImagePath($id);
		// return $path;
		echo($path);
		// echo("");
		// echo new JsonResponse($result);
		JFactory::getApplication()->close();
	}
	public function getFireAlarmDevicesCount()
	{
		$app = JFactory::getApplication();		
		header('Access-Control-Allow-Origin: *');
		echo(FloorHelpersFloor::getFireAlarmDevicesCount());
		JFactory::getApplication()->close();
	}
	public function getFireAlarmStatus()
	{
		$app = JFactory::getApplication();
		$data = new stdClass();
		$devices = FloorHelpersFloor::getFireAlarmDevicesData();
		$data->floors = FloorHelpersFloor::getFireAlarmFloorsData();
		$grouped_devices = array();
		foreach ($devices as $key => $device) {
			$grouped_devices[$device->floor][] = $device;
		}
		// $to_be_removed_floors = array();
		foreach ($data->floors as $key => $floor) {
			// $floor_id = $floor->id;			
			$devs = $grouped_devices[$data->floors[$key]->id];
			if ($devs && count($devs)>0)
			{
				$data->floors[$key]->devices = $devs;			
			}
			else
			{
				$data->floors[$key]->devices = array();
			}
			// if (count($data->floors[$key]->devices) <1) {
			// 	array_push($key);
			// }
		}
		// foreach ($to_be_removed_floors as $key) {
		// 	unset($data->floors[$key]);
		// }

		// $temperatureSensorData = FloorHelpersFloor::getTemperatureSensorData();
		// $temperatureSensorData = FloorHelpersFloor::getTemperatureSensorData();
		// $waterMeterData = FloorHelpersFloor::getWaterMeterData();
		// $liuchuanElevatorData = FloorHelpersFloor::getLiuchuanElevatorData();
		// $smartLampData = FLoorHelpersFloor::getSmartLampData();
		// // $temperatureSensorData =
		// $resultData = new stdClass();
		// // $coSensorData = array();
		// $resultData->coSensorData = $coSensorData;
		// $resultData->temperatureSensorData = $temperatureSensorData;
		// $resultData->waterMeterData = $waterMeterData;
		// $resultData->smartLampData = $smartLampData;
		// $resultData->liuchuanElevatorData = $liuchuanElevatorData;
		header('Access-Control-Allow-Origin: *');
		echo(json_encode($data));
		// echo("");
		JFactory::getApplication()->close();
	}
	public function getMain()
	{
		$app  = JFactory::getApplication();
		echo (json_encode(FloorHelpersFloor::getTotalSoyalPowerForHome()));
		JFactory::getApplication()->close();
	}
	public function getRealtimeDeviceDataByFloorId()
	{
		$app  = JFactory::getApplication();
		$floor_id = $app->input->get('floor_id',0);
		$result = new StdClass();
		$result->deviceData = FloorHelpersFloor::getAllEnableDeviceDataByFloor($floor_id);
		
		$mute_nodes = FloorHelpersFloor::getDioSupressedAlarmNodes();
		$result->allFloorAlarmCount = FloorHelpersFloor::getAllFloorAlarmCount($mute_nodes);
		$build_alarm_counts = FloorHelpersFloor::getAllBuildingAlarmCount($mute_nodes);		
		$result->allBuildingAlarmCount = $build_alarm_counts;
		$menus = FloorHelpersFloor::getAllMenuAlarmCount();
		foreach ($menus as $key => $menu) {
			if (count($menu->buildings) > 0)
			{
				$menus[$key]->count = 0;
				if ($menu->reversed)
				{
					foreach ($build_alarm_counts as $key1 => $building_count) 
					{
						if (!in_array($building_count->building, $menu->buildings))
						{							
							$menus[$key]->count += intval($building_count->alarm_count);	
						}

					}
				}
				else
				{
					foreach ($menu->buildings as $key1 => $building) 
					{					
						foreach ($build_alarm_counts as $key2 => $alarm_count)
						{
							if ($building == $alarm_count->building)
							{							
								$menus[$key]->count += intval($alarm_count->alarm_count);	
							}
						}
					}
				}
			}
		}
		$result->allMenuAlarmCount = (array)array_filter($menus,function($menu,$key) {
			return $menu->count > 0;
		},ARRAY_FILTER_USE_BOTH);	
		$result->allMenuAlarmCount = $menus;	
		echo (json_encode($result));
		JFactory::getApplication()->close();
	}
	public function getRealtimeDeviceData()
	{
		$app  = JFactory::getApplication();
		$floor_id = $app->input->get('floor_id',0);
		$coSensorData = FloorHelpersFloor::getCoSensorData();
		$pmSensorData = FloorHelpersFloor::getPmSensorData();
		// $temperatureSensorData = FloorHelpersFloor::getTemperatureSensorData();
		$temperatureSensorData = FloorHelpersFloor::getTemperatureSensorData();
		$waterMeterData = FloorHelpersFloor::getWaterMeterData($floor_id);
		$liuchuanElevatorData = FloorHelpersFloor::getLiuchuanElevatorData();
		$mitsubishiElevatorData = FloorHelpersFloor::getMitsubishiElevatorData();
		$fujiElevatorData = FloorHelpersFloor::getfujiElevatorData();
		$jetecWindDirectionData = FloorHelpersFloor::getJetecWindDirectionData();
		$weemaIaqData = FloorHelpersFloor::getWeemaIaqData();
		$jetecRainMeterData = FloorHelpersFloor::getJetecRainMeterData();
		$jetecSoilMeterData = FloorHelpersFloor::getJetecSoilMeterData();
		$irtiIvaPersonDetectionData = FloorHelpersFloor::getIrtiIvaPersonDetectionData();
		$irtiIvaPersonCountingData = FloorHelpersFloor::getIrtiIvaPersonCountingData();
		$smartLampData = FLoorHelpersFloor::getSmartLampData();
		$electronicMeterData = FLoorHelpersFloor::getElectronicMeterData($floor_id);
		$solarMeterData = FLoorHelpersFloor::getSolarMeterData($floor_id);
		$analogData = FLoorHelpersFloor::getAnalogData($floor_id);
		// $temperatureSensorData =
		$resultData = new stdClass();
		// $coSensorData = array();
		$resultData->coSensorData = $coSensorData;
		$resultData->pmSensorData = $pmSensorData;
		$resultData->temperatureSensorData = $temperatureSensorData;
		$resultData->waterMeterData = $waterMeterData;
		$resultData->smartLampData = $smartLampData;
		$resultData->liuchuanElevatorData = $liuchuanElevatorData;
		$resultData->mitsubishiElevatorData = $mitsubishiElevatorData;
		$resultData->fujiElevatorData = $fujiElevatorData;
		$resultData->jetecWindDirectionData = $jetecWindDirectionData;
		$resultData->weemaIaqData = $weemaIaqData;
		$resultData->jetecRainMeterData = $jetecRainMeterData;
		$resultData->jetecSoilMeterData = $jetecSoilMeterData;
		$resultData->irtiIvaPersonDetectionData = $irtiIvaPersonDetectionData;
		$resultData->irtiIvaPersonCountingData = $irtiIvaPersonCountingData;
		$resultData->electronicMeterData = $electronicMeterData;
		$resultData->solarMeterData = $solarMeterData;
		$resultData->analogData = $analogData;
		echo(json_encode($resultData));
		// echo("");
		JFactory::getApplication()->close();
	}
	public function getLogStatus()
	{
		$app  = JFactory::getApplication();

		$obj = new stdClass;

		$ids = $app->input->json->get('id', array(), 'array');
		$mydevs = $app->input->json->get('dev', array(), 'array');

    FloorHelpersFloor::closeCCTVDevice($mydevs);
		$items = FloorHelpersFloor::getLogStatus($ids);

    $items1 = FloorHelpersFloor::findMaxLogId();
    $obj->max = 0;
		if(count($items1))
		{
			  $obj->floor = $items1[0]->note2;
		    $obj->max = $items1[0]->id;
				$obj->building = $items1[0]->note3;
				$obj->node_id = $items1[0]->device_id;
				$obj->alarm_sound_file = $items1[0]->alarm_sound_file;

    }

		$obj->items = $items;
		$obj->is_need_alarm = FloorHelpersFloor::is_need_alarm();
		$json = json_encode($obj);
		//$json = json_encode($obj->id);

		echo($json);
		JFactory::getApplication()->close();

	}

	public function getLog()
  {
		  $app  = JFactory::getApplication();

      $obj = new stdClass;

		  $obj->id = $app->input->json->getInt('id',0);
		  $obj->check_name = $app->input->json->getVar('name',0);

      $items = FloorHelpersFloor::getDevicelog($obj->id);

      $obj->status = 2;

			if(count($items))
			{
				  if($items[0]->status == 1)
					{
						$obj->status = 2;
					}
					else if($items[0]->status != 2)
					{
						$obj->status = 0;
					}

          $obj->floor = $items[0]->note2;
					$obj->building = $items[0]->note3;
					$obj->group = $items[0]->note4;
					$obj->node_id = $items[0]->note5;

			}

			$json = json_encode($obj);

	    echo($json);
			JFactory::getApplication()->close();

  }
	public function setCheck()
  {
		  $app  = JFactory::getApplication();

      $obj = new stdClass;

		  $obj->id = $app->input->json->getInt('id',0);
		  $obj->check_name = $app->input->json->getVar('name',0);

      $items = FloorHelpersFloor::getDevicelog($obj->id);

      $obj->check_date = date("Y/m/d");
			$obj->check_time = date("H:i:s");

      $obj->status = 2;

			if(count($items))
			{
				  if($items[0]->status == 1)
					{
						$obj->status = 2;
					}
					else if($items[0]->status != 2)
					{
						$obj->status = 0;
					}

          $obj->floor = $items[0]->note2;
					$obj->building = $items[0]->note3;
					$obj->group = $items[0]->note4;
					$obj->node_id = $items[0]->note5;
			    FloorHelpersFloor::updateDevicelog($items[0],$obj);
			}

			$json = json_encode($obj);

	    echo($json);
			JFactory::getApplication()->close();

  }
	public function do_update_device_state($serverip,$mytoken,$num_arr,$items,$arr)
	{

		if(empty($num_arr))
		{
			echo("empty(num_arr)");
			return;
		}

		$arr["extensions"] = $num_arr;
		$json = json_encode($arr);

		$addr = "http://" . $serverip.":8899/api/extensions/status";

    $file = "/var/www/html/STATUS1";

    if(count($items))
		{
        if($items[0]->note1 == 13)
				{
					$file = "/var/www/html/STATUS2";
				}
		}

    //$cmd = '1234';
		$cmd = "curl -v -X POST " . $addr;
    $cmd = $cmd.' -H "Content-Type: application/json" -H "access_token: '.$mytoken;
		$cmd = $cmd.'"'. " -d '".$json."'";
		$cmd = $cmd." --output ".$file;

    system($cmd);
		echo($cmd);
		//return;

		//$myFile = "/var/www/html/STATUS1";

		$content = JFile::read($file);

		if(!empty($content))
		{
					  //var_dump(json_decode($content, true));

        echo($content);
				FloorHelpersFloor::save_device_status(json_decode($content,true),$items);

		}
		else
		{
			echo("err update_device_state");
		}

	}

	public function do_updatesip_device_state($serverip,$mytoken,$num_arr,$arr,$sip)
	{

		if(empty($num_arr))
		{
			//echo("empty(num_arr)");
			return ;
		}

		$arr["extensions"] = $num_arr;
		$json = json_encode($arr);

		$addr = "http://" . $serverip.":8899/api/extensions/status";

    $file = "/var/www/html/STATUS1";

    //$cmd = '1234';
		$cmd = "curl -v -X POST " . $addr;
    $cmd = $cmd.' -H "Content-Type: application/json" -H "access_token: '.$mytoken;
		$cmd = $cmd.'"'. " -d '".$json."'";
		$cmd = $cmd." --output ".$file;

    system($cmd);
		//echo($cmd);
		//return;

		//$myFile = "/var/www/html/STATUS1";

		$content = JFile::read($file);

		if(!empty($content))
		{
				//$content = json_decode($content, true);
				//FloorHelpersFloor::save_device_status(json_decode($content,true),$items);
				$sip->content[] = json_decode($content, true);

		}

	}
	public  function getOpenDevices()
    {
		$app  = JFactory::getApplication();
		// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
				 ->select('DISTINCT a.*');

		 $query->from('`#__device_table` AS a');

		 $query->where('a.state = 1');

		 $query->where('a.enable = 1');

		 //$query->where('a.type = 3');

		 //$query->where('a.dio_id = 0');

		 $query->where('a.open = 1');

		 $query->where('a.path != ""');
		 $query->where('a.type = 1');
		 $query->order($db->escape('a.update DESC ,a.update_time  DESC'));
		 
		 $query->setLimit(1);

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();
		 $device_brands = FloorHelpersFloor::getDeviceBrands();
		 for ($i=0; $i < count($items); $i++) {
			 $items[$i]->web_view_subpath = "/";
			 $items[$i]->web_view_port = "80";
			 $items[$i]->web_view_scheme = "http";
			 if ($items[$i]->device_brand_id !=0) {
				for ($j=0; $j < count($device_brands); $j++) {
					// $device_brand = $device_brands[$j];
					if ($device_brands[$j]->id == $items[$i]->device_brand_id) {
						$items[$i]->web_view_subpath = $device_brands[$j]->web_view_subpath;
						$items[$i]->web_view_port = $device_brands[$j]->web_view_port;
						$items[$i]->web_view_scheme = $device_brands[$j]->web_view_scheme;
					}

				}
			 }
		 }
		 $test;
		 $result_item = new StdClass();
			foreach($items as $i=>$item)
			{
				$result_item->dio_id = +$item->dio_id;
				$result_item->id = +$item->id;
				$result_item->name = $item->path;
				$result_item->open = +$item->open;
				$result_item->path = $item->path;
				$result_item->open1 = +$item->open1;
				$result_item->path1 = $item->path1;
				$result_item->open2 = +$item->open2;
				$result_item->path2 = $item->path2;
				$result_item->open3 = +$item->open3;
				$result_item->path3 = $item->path3;
				$result_item->web_view_subpath = isset($item->web_view_subpath)?$item->web_view_subpath:"";
				$result_item->web_view_port = isset($item->web_view_port)?$item->web_view_port:"";
				$result_item->web_view_scheme = isset($item->web_view_scheme)?$item->web_view_scheme:"";
			
				$result_item->web_view_subpath1 = isset($item->web_view_subpath1)?$item->web_view_subpath1:"";
				$result_item->web_view_port1 = isset($item->web_view_port1)?$item->web_view_port1:"";
				$result_item->web_view_scheme1 = isset($item->web_view_scheme1)?$item->web_view_scheme1:"";

				$result_item->web_view_subpath2 = isset($item->web_view_subpath2)?$item->web_view_subpath2:"";
				$result_item->web_view_port2 = isset($item->web_view_port2)?$item->web_view_port2:"";
				$result_item->web_view_scheme2 = isset($item->web_view_scheme2)?$item->web_view_scheme2:"";

				$result_item->web_view_subpath3 = isset($item->web_view_subpath3)?$item->web_view_subpath3:"";
				$result_item->web_view_port3 = isset($item->web_view_port3)?$item->web_view_port3:"";
				$result_item->web_view_scheme3 = isset($item->web_view_scheme3)?$item->web_view_scheme3:"";
				$result_item->group = +$item->group;
				
	
					}
					if (count($items) > 0)
					{
					//	$result_item = htmlentities($result_item, UTF-8);
						// echo(json_encode([$result_item], JSON_NUMERIC_CHECK));
						echo(json_encode([$result_item]));
					}
					else
					{
						echo(json_encode([]));
					}
				// echo($test);
		 $app->close();


		 return json_encode([$test]);
		}

		public function call_window_close(){
			$app  = JFactory::getApplication();
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query
			->select('DISTINCT a.*');

			$query->from('`#__device_table` AS a');

			$query->where('a.state = 1');

			$query->where('a.enable = 1');

			$query->where('a.open = 0');
			$query->order($db->escape('a.update DESC ,a.update_time  DESC'));
			
			$query->setLimit(1);
			$db->setQuery($query);
			$items = (array) $db->loadObjectList();
			foreach($items as $i=>$item)
			{
				$id = +$item->dio_alarm;
			}

			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query	->select('DISTINCT a.*');

			$query->from('`#__top_table` AS a');
			$query->where($db->quoteName('id') . ' = ' . $db->quote($id));
			$query->setLimit(1);
			
			$db->setQuery($query);
			$items = (array) $db->loadObjectList();
			foreach($items as $i=>$item)
			{
			$result_item = +$item->cctv_timeout;
			}
			if($result_item=="")
			{
				$result_item = 0;
			}

			//Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
			$query
					->select('DISTINCT a.*');

			$query->from('`#__device_table` AS a');

			$query->where('a.state = 1');

			$query->where('a.enable = 1');

			$query->where('TIMESTAMP(a.last_alarm_time)>=date_sub(now(),  INTERVAL ' . $db->quote($result_item) . ' second) ');
			$query->where('a.open = 0');
			$query->order($db->escape('a.last_alarm_time DESC'));

			$query->setLimit(1);
			$db->setQuery($query);

			$items = (array) $db->loadObjectList();
			if(count($items))
			{
				$minutes = 0;
			}
			else
			{
				//Create a new query object.
				$db = JFactory::getDbo();
				$query = $db->getQuery(true);

				// Select the required fields from the table.
				$query
				->select('DISTINCT a.*');

				$query->from('`#__device_table` AS a');

				$query->where('a.state = 1');

				$query->where('a.enable = 1');

				$query->where('a.open = 1');

				$query->order($db->escape('a.update DESC ,a.update_time  DESC'));
				
				$query->setLimit(1);

				$db->setQuery($query);

				$items = (array) $db->loadObjectList();

				if(count($items))
				{
					$minutes = 0;
				}
				else
				{
					$minutes = 1;
				}
				
			}
			echo($minutes);
			$app->close();
		}
		public function checkIsChangeSettingDone(){
			$app  = JFactory::getApplication();
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query
			->select('DISTINCT a.*');

			$query->from('`#__device_table` AS a');

			$query->where('a.is_change_setting = 1');

			$query->order($db->escape('a.last_alarm_time ASC'));

			$db->setQuery($query);
			$items = (array) $db->loadObjectList();
			$result_item = array();
			foreach($items as $i=>$item)
			{
				$result_item [] = array('last_alarm_time'=>$item->last_alarm_time,'dio_alarm'=>+$item->dio_alarm,'id'=> +$item->id);
			}
			$query = $db->getQuery(true);
			
			$query	->select('DISTINCT a.*');

			$query->from('`#__top_table` AS a');
			foreach($result_item as $i=>$item)
			{
				if($i==0)
				{
					$str = $str.$item["dio_alarm"];
				}
				else
				{
					$str = $str." OR  `id` = ".$item["dio_alarm"];
				}
			}
			$query->where($db->quoteName('id') . ' = ' . $db->quote($str));

			$db->setQuery($query);
			$items = (array) $db->loadObjectList();
			$result_item1 = array();
			foreach($items as $i=>$item)
			{
				$result_item1[] = array('id'=>+$item->id,'cctv_timeout'=>+$item->cctv_timeout);
			}

			$result_item2 = array();

			foreach($result_item as $i=>$item)
			{
				foreach($result_item1 as $i=>$item1)
				{
					if($item["dio_alarm"]==$item1["id"])
					{
						$last_alarm_time1 = strtotime($item["last_alarm_time"]);
						$today = date('H:i:s');
						$last_alarm_time2 = strtotime($today);
						if((($last_alarm_time1+$item1["cctv_timeout"])-$last_alarm_time2)<0)
						{
							$result_item2[] = array('id'=>+$item["id"]);
						}
					}
				}
			}
			if(count($result_item2))
			{
				$query = $db->getQuery(true);

				$query->update($db->quoteName('#__device_table'));
				$query->set($db->quoteName('is_change_setting') . ' = ' . $db->quote(0));
		
				foreach($result_item2 as $i=>$item)
				{
					if($i==0)
					{
						$str1 = $str1.$item["id"];
					}
					else
					{
						$str1 = $str1." OR  `id` = ".$item["id"];
					}
				}
				$query->where($db->quoteName('id') . ' = ' . $db->quote($str1));

				$db->setQuery($query)->execute();
			}
			$app->close();
	
		}
		public function KCtest()
		{
			$app  = JFactory::getApplication();

				echo(json_encode($items));
				$app->close();
		}
}
