<?php
/**
 * @version    CVS: 1.0.0
 * @package    Com_Top
 * <AUTHOR> lo <<EMAIL>>
 * @copyright  2019 kevin lo
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
// No direct access
defined('_JEXEC') or die;

use \Joomla\CMS\HTML\HTMLHelper;
use \Joomla\CMS\Factory;
use \Joomla\CMS\Uri\Uri;
use \Joomla\CMS\Router\Route;
use \Joomla\CMS\Language\Text;

HTMLHelper::addIncludePath(JPATH_COMPONENT . '/helpers/html');
HTMLHelper::_('bootstrap.tooltip');
HTMLHelper::_('behavior.multiselect');
HTMLHelper::_('formbehavior.chosen', 'select');

$user       = Factory::getUser();
$userId     = $user->get('id');
$listOrder  = $this->state->get('list.ordering');
$listDirn   = $this->state->get('list.direction');
$canCreate  = $user->authorise('core.create', 'com_top') ;//&& file_exists(JPATH_COMPONENT . DIRECTORY_SEPARATOR . 'models' . DIRECTORY_SEPARATOR . 'forms' . DIRECTORY_SEPARATOR . 'tableform.xml');
$canEdit    = $user->authorise('core.edit', 'com_top') && file_exists(JPATH_COMPONENT . DIRECTORY_SEPARATOR . 'models' . DIRECTORY_SEPARATOR . 'forms' . DIRECTORY_SEPARATOR . 'tableform.xml');
$canCheckin = $user->authorise('core.manage', 'com_top');
$canChange  = $user->authorise('core.edit.state', 'com_top');
$canDelete  = $user->authorise('core.delete', 'com_top');

$myid = $this->state->get('list_myid');

$top_id = $this->state->get('top_id');

$supergroup = TopHelpersTop::get_dio_supergroup();

$top_floors = TopHelpersTop::get_dio_top_floor();

foreach($top_floors as $i=>$item)
{
  $floors = TopHelpersTop::get_top_floor($item->id);
  $item->floors = $floors;
}

$alarmdirs = TopHelpersTop::get_dio_alarmdir_top();
foreach($alarmdirs as $i=>$item)
{
  $alarms = TopHelpersTop::get_dio_alarm_top($item->id);
  $item->alarms = $alarms;
}


?>
<style>
.my-div{
  margin:0 auto;
  width:1800px;
  height:100%;
}
.my-top-div{
  margin-left:0px;
  margin-top:20px;
  width:1700px;
  height:100%;
  padding-left:20px;
  padding-top:20px;
  border: 1px solid green;
}
.myinput{
  margin-left:20px;
}
.mycheckbox{
  outline: 1px solid #74b2de
}
.myth {

  background-color: #74b2de;
}
.mymenu
{
	font-size: 18px;

}
</style>
<form action="<?php echo htmlspecialchars(Uri::getInstance()->toString()); ?>" method="post"
      enctype="multipart/form-data" name="adminForm" id="adminForm">

<div class="my-div">
  <?php if ($canCreate) : ?>
    <button id="save1" class="btn" type="button"><img src="/media/com_top/img/儲存-01.png" width="68" height="26" ></button>

  <?php endif; ?>
  <button id="close1" class="btn" type="button"><img src="/media/com_top/img/關閉-01.png" width="68" height="26" ></button>
<div class="my-top-div">

  <p>名稱<input type="text" class="myinput" name="name" id="name" value="<?php echo $this->items[0]->name;?>"></p>
  <?php if($this->items[0]->vendor != 1 && $this->items[0]->vendor < 100):?>
  <p>起始位址<input type="text" class="myinput" name="addr" id="addr" value="<?php echo $this->items[0]->addr;?>"></p>
  <?php if($this->items[0]->vendor == TopHelpersTop::$dio_weema_485_vendor):?>
    <input type="hidden" name="dio_type1" value="<?php echo $this->items[0]->dio_type;?>"/>
  <?php elseif(
    $this->items[0]->vendor == TopHelpersTop::$dio_liuchuanElevator_vendor || 
    $this->items[0]->vendor == TopHelpersTop::$dio_mitsubishiElevator_vendor || 
    $this->items[0]->vendor == TopHelpersTop::$dio_yunyangFireFighter_vendor ||
    $this->items[0]->vendor == TopHelpersTop::$dio_baochungFireFighter_vendor ||
    $this->items[0]->vendor == TopHelpersTop::$dio_fujiElevator_vendor
    ):?>
    
  <?php else:?>
  <select name="dio_type1">
    <?php foreach(TopHelpersTop::$dio_types as $i1=>$item1):?>
     <option value="<?php echo $i1;?>" <?php if($this->items[0]->dio_type == $i1) echo("selected=selected");?>><?php echo $item1?></option>\
   <?php endforeach;?>

  </select>
<?php endif;?>
  <div></div>
<?php endif;?>
    <?php if($myid > 0):?>
  <table id="main_table">
    <tr>
      <th class="myth"><font color="white">啟動</font></th>
      <th class="myth"><font color="white">編號</font></th>
      <th class="myth"><font color="white" title="是否顯示點位名稱？此功能不適用特殊樓層">名</font></th>
      <th class="myth"><font color="white" title="是否觸發警報？此功能僅適用於DI點">警</font></th>
      <th class="myth"><font color="white" title="是否為故障點？此功能僅適用於DI點">故</font></th>
      <th class="myth"><font color="white" title="是否顯示運轉時間？此功能僅適用於DI點">間</font></th>
      <th class="myth"><font color="white" title="是否啟用BA APP功能？此功能僅適用於DI點及水電錶">A</font></th>
      <th class="myth"><font color="white">位址</font></th>
    <?php if(
          $this->items[0]->vendor == TopHelpersTop::$dio_yunyangFireFighter_vendor ||
          $this->items[0]->vendor == TopHelpersTop::$dio_baochungFireFighter_vendor): ?>
      <th class="myth"><font color="white">對照</font></th>
    <?php endif; ?>
      <th class="myth"><font color="white">DI/DO</font></th>
      <th class="myth"><font color="white">NC/NO</font></th>

      <th class="myth"><font color="white">名稱</font></th>
      <th class="myth"><font color="white">位址</font></th>

      <th class="myth"><font color="white">字型大小</font></th>
      <th class="myth"><font color="white">顏色</font></th>
      <th class="myth"><font color="white">監控點資料夾</font></th>
      <th class="myth"><font color="white">監控點</font></th>
      <th class="myth"><font color="white">列表</font></th>
      <th class="myth"><font color="white">樓層</font></th>
      <th class="myth"><font color="white">連動資料夾</font></th>
      <th class="myth"><font color="white">連動</font></th>
      <?php if ($this->items[0]->vendor == TopHelpersTop::$dio_weema_485_vendor 
              && (
                $this->items[0]->dio_type == TopHelpersTop::$dio_co_sensor_dio_type || $this->items[0]->dio_type == TopHelpersTop::$dio_co_sensor_general_dio_type || $this->items[0]->dio_type == TopHelpersTop::$dio_co_sensor_yon_gjia_dio_type) ): ?>
            <th class="myth"><font color="white">連動閾值</font></th>            
      <?php endif; ?>
      
      
    </tr>
    <?php $ref_index = $this->items[0]->addr; ?>
    <?php $ref_sub_item_index = 0; ?>
    <?php $offset_index = 0; ?>
    <?php foreach($this->devices as $i=>$item): ?>

    <tr>
      <td>
        <input class="mycheckbox" type="checkbox" id="enable<?php echo $i;?>" value="<?php echo $item->enable;?>" <?php if($item->enable == 1) echo ("checked");?>></td>
        <input type="hidden" name="device_enable[]" id="myenable<?php echo $i;?>" value="0"/></td>
        <td><?php
          if($item->index == 0)
              echo($item->id);
          else
              echo $item->index;
      ?></td>
      <td title="是否顯示點位名稱？此功能不適用特殊樓層">
        <input class="mycheckbox show_node_text_checkbox" type="checkbox" id="show_node_text<?php echo $i;?>" 
         <?php if($item->show_node_text == 1) echo ("checked");?>>
         <input type="hidden" class="show_node_text_hidden" name="show_node_texts[]"  value="<?php echo ($item->show_node_text); ?>"/>
        </td>
      <td title="是否觸發警報？此功能僅適用於DI點">
        <input class="mycheckbox trigger_alarm_checkbox" type="checkbox" id="trigger_alarm<?php echo $i;?>" 
         <?php if($item->trigger_alarm == 1) echo ("checked");?>>
         <input type="hidden" class="trigger_alarm_hidden" name="trigger_alarms[]"  value="<?php echo ($item->trigger_alarm); ?>"/>
        </td>
      <td title="是否為故障點？此功能僅適用於DI點">
        <input class="mycheckbox is_fault_node_checkbox" type="checkbox" id="is_fault_node<?php echo $i;?>" 
         <?php if($item->is_fault_node == 1) echo ("checked");?>>
         <input type="hidden" class="is_fault_node_hidden" name="is_fault_nodes[]"  value="<?php echo ($item->is_fault_node); ?>"/>
        </td>
        <td title="是否顯示運轉時間？此功能僅適用於DI點">
        <input class="mycheckbox display_last_alarm_time_checkbox" type="checkbox" id="display_last_alarm_time<?php echo $i;?>" 
         <?php if($item->display_last_alarm_time == 1) echo ("checked");?>>
         <input type="hidden" class="display_last_alarm_time_hidden" name="display_last_alarm_times[]"  value="<?php echo ($item->display_last_alarm_time); ?>"/>
        </td>
        <td title="是否啟用BA APP功能？此功能僅適用於DI點及水電錶">
        <input class="mycheckbox ba_app_enable_checkbox" type="checkbox" id="ba_app_enable<?php echo $i;?>" 
         <?php if($item->ba_app_enable == 1) echo ("checked");?>>
         <input type="hidden" class="ba_app_enable_hidden" name="ba_app_enables[]"  value="<?php echo ($item->ba_app_enable); ?>"/>
        </td>
        <td>
          <?php echo $this->items[0]->addr + ($item->index-1) ?>
        </td>
        <?php 
          if($this->items[0]->vendor == TopHelpersTop::$dio_yunyangFireFighter_vendor): 
            echo "<td>";
            if($item->index%2 ==1):
              echo CEIL($this->items[0]->addr/512). "-".(CEIL($this->items[0]->addr%512/32) ). "-" .((1+($ref_index-1)*8+$ref_sub_item_index)%256). "-故障點";
            else:
              echo CEIL($this->items[0]->addr/512). "-".(CEIL($this->items[0]->addr%512/32) ). "-" .((1+($ref_index-1)*8+$ref_sub_item_index)%256) . "-運轉點";              
              $ref_sub_item_index++;
            endif;
            if ($item->index%8 ==0):
              // $ref_index++;
            endif;            
            // if ($ref_sub_item_index %4 == 0):
            //   $ref_sub_item_index
            // endif;
            echo "</td>";
          elseif ($this->items[0]->vendor == TopHelpersTop::$dio_baochungFireFighter_vendor):
            echo "<td>";            
            if($item->index%8 ==1):
              echo ($this->items[0]->addr + $ref_sub_item_index)."-火警";
            elseif ($item->index%8 == 3):
              echo ($this->items[0]->addr + $ref_sub_item_index)."-故障";
            elseif ($item->index%8 == 4):
              echo ($this->items[0]->addr + $ref_sub_item_index)."-隔離";
            elseif ($item->index%8 == 5):
              echo ($this->items[0]->addr + $ref_sub_item_index)."-告警";
            elseif ($item->index%8 == 6):
              echo ($this->items[0]->addr + $ref_sub_item_index)."-聯動";
            else:
              echo "NA";
            endif;
            if ($item->index%8 ==0):
              // $ref_index++;
              $ref_sub_item_index++;
            endif;            
            // if ($ref_sub_item_index %4 == 0):
            //   $ref_sub_item_index
            // endif;
            echo "</td>";
          endif;
        ?>
      <td>

        <?php if($this->items[0]->vendor == 8 || $this->items[0]->vendor == 4 || $this->items[0]->vendor == 11 || $this->items[0]->vendor == 13):?>
          <input type="hidden" name="dio_type[]" id="dio_type<?php echo $i;?>" value="<?php echo($item->dio_type);?>"/>
          <p> <?php echo(TopHelpersTop::$dio_types[$item->dio_type]);?></p>          
        <?php elseif($this->items[0]->vendor != 1):?>
          <input type="hidden" name="dio_type[]" id="dio_type<?php echo $i;?>" value="<?php echo($this->items[0]->dio_type);?>"/>
          <p> <?php echo(TopHelpersTop::$dio_types[$this->items[0]->dio_type]);?></p>
        <?php else:?>
        <select name="dio_type[]" style="width: 50px;">
          <?php if($item->index > 16):?>
          <?php foreach(TopHelpersTop::$dio_types as $i1=>$item1): ?>
          <option value="<?php echo $i1;?>" <?php if($i1==$item->dio_type) echo("selected=selected");?>><?php echo $item1;?></option>
        <?php endforeach;?>
      <?php else: ?>

        <option value="1" selected=selected >DI</option>

      <?php endif; ?>
        </select>
     <?php endif;?>
      </td>
      <td>
        <?php if($item->dio_type == 1): ?>
        <select name="nc[]" id="nc<?php echo $i;?>" style="width: 60px;">
          <option value="1" <?php if($item->nc=="1") echo("selected=selected");?>>NC</option>
          <option value="0" <?php if($item->nc=="0") echo("selected=selected");?>>NO</option>
        </select>
        <?php else: ?>
          N/A
          <input type="hidden" name="nc[]" value="0" />
        <?php endif; ?>
      </td>
      <td>
      <input type="text" name="note[]" value="<?php echo $item->note?>">
      </td>

        <td>
        <input type="text" name="info[]" value="<?php echo $item->info?>">
        </td>

      <td>
        <select name="fontsize[]" style="width: 60px;">
          <?php foreach(TopHelpersTop::$fontsizes as $i1=>$item1): ?>
          <option value="<?php echo $i1;?>" <?php if($i1==$item->fontsize) echo("selected=selected");?>><?php echo $item1;?></option>
        <?php endforeach;?>
          </select>
      </td>
      <td>
        <select name="color[]" style="width: 50px;">
          <?php foreach(TopHelpersTop::$colors as $i1=>$item1): ?>
          <option value="<?php echo $i1;?>" <?php if($i1==$item->color) echo("selected=selected");?>><?php echo $item1;?></option>
        <?php endforeach;?>
          </select>

      </td>
      <td>
        <select name="supergroup[]" style="width: 120px;" onchange="groupChange(this,<?php echo $i;?>)">
          <?php foreach($supergroup as $i1=>$item1): ?>
          <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->supergroup) echo("selected=selected");?>><?php echo $item1->name;?></option>
        <?php endforeach;?>
          </select>

      </td>
      <td>
        <span id="group_span<?php echo $i;?>">
        <select name="group[]" style="width: 120px;" >
       <?php
           $sgroupid = $supergroup[0]->id;
           if(!empty($item->supergroup) && $item->supergroup > 0)
           {

               $sgroupid = $item->supergroup;
           }

            //JLog::add('super '.$sgroupid, JLog::INFO, 'jerror');
            $groups =  TopHelpersTop::get_dio_group($sgroupid);
         ?>
          <?php foreach($groups as $i1=>$item1): ?>
          <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->group) echo("selected=selected");?>><?php echo $item1->name;?></option>
        <?php endforeach;?>
      </select>
    </span>
      </td>
      <td>
        <span id="top_floor_span<?php echo $i;?>">
        <select name="top_floor[]" id="top_floor<?php echo $i;?>" style="width: 120px;" onchange="myChange(this,<?php echo$i;?>)">
          <?php foreach($top_floors as $i1=>$item1): ?>
          <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->type) echo("selected=selected");?>><?php echo $item1->name;?></option>
        <?php endforeach;?>
      </select>
    </span>
      </td>
      <td>
        <span id="myspan<?php echo $i;?>">
        <select name="dio_floor[]" id="dio_floor<?php echo $i;?>" style="width: 100px;">
          <?php
          $floors = $top_floors[0]->floors;
          foreach($top_floors as $i1=>$item1)
          {
              if($item1->id == $item->type)
              {
                $floors = $item1->floors;
                break;
              }
          }
          ?>
          <?php foreach($floors as $i1=>$item1): ?>
          <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->floor) echo("selected=selected");?>><?php echo $item1->name;?></option>
        <?php endforeach;?>
      </select>
      </span>
      </td>

      <td>

        <select name="dio_alarmdir[]" style="width: 100px;" onchange="alarmChange(this,<?php echo$i;?>)">
          <?php foreach($alarmdirs as $i1=>$item1): ?>
          <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->dio_alarmdir) echo("selected=selected");?>><?php echo $item1->name;?></option>
        <?php endforeach;?>
      </select>

      </td>

      <td>
        <span id="dio_alarm_span<?php echo $i;?>">
        <select name="dio_alarm<?php echo $i;?>" id="dio_alarm<?php echo $i;?>" style="width: 100px;">

          <?php
          $alarms = $alarmdirs[0]->alarms;
          foreach($alarmdirs as $i1=>$item1)
          {
              if($item1->id == $item->dio_alarmdir)
              {
                $alarms = $item1->alarms;
                break;
              }
          }
          ?>

          <?php foreach($alarms as $i1=>$item1): ?>
          <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->dio_alarm) echo("selected=selected");?>><?php echo $item1->name;?></option>
        <?php endforeach;?>
      </select>
    </span>
      </td>
      <?php if ($this->items[0]->vendor == TopHelpersTop::$dio_weema_485_vendor && ($this->items[0]->dio_type == TopHelpersTop::$dio_co_sensor_dio_type || $this->items[0]->dio_type == TopHelpersTop::$dio_co_sensor_general_dio_type || $this->items[0]->dio_type == TopHelpersTop::$dio_co_sensor_yon_gjia_dio_type))  : ?>
        <td>
        <input type="number" step="1" name="co_sensor_alarm_threshold[]" style="width: 80px;" value="<?php echo $item->co_sensor_alarm_threshold?>">
        </td>
      <?php else: ?>
        <!-- <input type="hidden" name="co_sensor_alarm_threshold[]" value="<?php echo $item->co_sensor_alarm_threshold?>"> -->
      <?php endif; ?>
      
    </tr>

    <?php if($this->items[0]->vendor == TopHelpersTop::$dio_weema_485_vendor  && 
    (
      $this->items[0]->dio_type == TopHelpersTop::$dio_irti_iva_person_counting_dio_type 
    || $this->items[0]->dio_type == TopHelpersTop::$dio_irti_iva_person_detection_dio_type 
    || $this->items[0]->dio_type == TopHelpersTop::$dio_co2_dio_type)): ?>
      <tr>
        <td colspan="20">
          連動閾值
          <input type="number" style="width:80px;" name="alarm_threshold[]" value="<?php echo $item->alarm_threshold;?>"/>
        </td>
      </tr>
    <?php 
    elseif ($this->items[0]->vendor == TopHelpersTop::$dio_weema_485_vendor 
    && ($this->items[0]->dio_type == TopHelpersTop::$dio_temp_humidity_dio_type 
    ||$this->items[0]->dio_type == TopHelpersTop::$dio_weema_iaq_dio_type
    || $this->items[0]->dio_type == TopHelpersTop::$dio_yon_gjia_temp_humidity_dio_type
    || $this->items[0]->dio_type == TopHelpersTop::$dio_yon_gjia_temp_lux_dio_type
    || $this->items[0]->dio_type == TopHelpersTop::$dio_yon_gjia_temp_humidity2_dio_type
    || $this->items[0]->dio_type == TopHelpersTop::$dio_pm_sensor_yon_gjia_dio_type
    || $this->items[0]->dio_type == TopHelpersTop::$dio_yon_gjia_temp_humidity_3_in_1_dio_type
    || $this->items[0]->dio_type == TopHelpersTop::$dio_temp_humidity_pinron_dio_type
    || $this->items[0]->dio_type == TopHelpersTop::$dio_jnc_temp_humidity_dio_type )): ?>        
      <tr>
        <td colspan="20">
          溫度連動閾值
          <input type="number" style="width:80px;" name="temperature_alarm_threshold[]" value="<?php echo $item->temperature_alarm_threshold;?>"/>
          溫度連動資料夾
          <select name="temperature_alarmdir[]" style="width: 100px;" onchange="temperatureAlarmChange(this,<?php echo$i;?>)">
              <?php foreach($alarmdirs as $i1=>$item1): ?>
              <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->temperature_alarmdir) echo("selected=selected");?>><?php echo $item1->name;?></option>
            <?php endforeach;?>
          </select>
          <!-- <input type="number" style="width:80px;" name="temperature_alarmdir[]" value="<?php echo $item->temperature_alarmdir;?>"/> -->
          溫度連動
          <span id="temperature_alarm_span<?php echo $i;?>">
              <select name="temperature_alarm[]" id="temperature_alarm<?php echo $i;?>" style="width: 100px;">

                <?php
                $alarms = $alarmdirs[0]->alarms;
                foreach($alarmdirs as $i1=>$item1)
                {
                    if($item1->id == $item->temperature_alarmdir)
                    {
                      $alarms = $item1->alarms;
                      break;
                    }
                }
                ?>

                <?php foreach($alarms as $i1=>$item1): ?>
                <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->temperature_alarm) echo("selected=selected");?>><?php echo $item1->name;?></option>
              <?php endforeach;?>
            </select>
          </span>
          </br>
          <?php if ($this->items[0]->dio_type == TopHelpersTop::$dio_yon_gjia_temp_lux_dio_type): ?>
            照度連動閾值
            <input type="number" style="width:80px;" name="lux_alarm_threshold[]" value="<?php echo $item->lux_alarm_threshold;?>"/>
            照度連動資料夾
            <select name="lux_alarmdir[]" style="width: 100px;" onchange="luxAlarmChange(this,<?php echo$i;?>)">
                <?php foreach($alarmdirs as $i1=>$item1): ?>
                <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->lux_alarmdir) echo("selected=selected");?>><?php echo $item1->name;?></option>
              <?php endforeach;?>
            </select>
            
            濕度連動
            <span id="lux_alarm_span<?php echo $i;?>">
                <select name="lux_alarm[]" id="lux_alarm<?php echo $i;?>" style="width: 100px;">

                  <?php
                  $alarms = $alarmdirs[0]->alarms;
                  foreach($alarmdirs as $i1=>$item1)
                  {
                      if($item1->id == $item->lux_alarmdir)
                      {
                        $alarms = $item1->alarms;
                        break;
                      }
                  }
                  ?>

                  <?php foreach($alarms as $i1=>$item1): ?>
                  <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->lux_alarm) echo("selected=selected");?>><?php echo $item1->name;?></option>
                <?php endforeach;?>
              </select>
            </span>
            </br>
          <?php endif; ?>
          <?php if ($this->items[0]->dio_type != TopHelpersTop::$dio_temp_humidity_pinron_dio_type): ?>
          濕度連動閾值
          <input type="number" style="width:80px;" name="humidity_alarm_threshold[]" value="<?php echo $item->humidity_alarm_threshold;?>"/>
          濕度連動資料夾
          <select name="humidity_alarmdir[]" style="width: 100px;" onchange="humidityAlarmChange(this,<?php echo$i;?>)">
              <?php foreach($alarmdirs as $i1=>$item1): ?>
              <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->humidity_alarmdir) echo("selected=selected");?>><?php echo $item1->name;?></option>
            <?php endforeach;?>
          </select>
          
          濕度連動
          <span id="humidity_alarm_span<?php echo $i;?>">
              <select name="humidity_alarm[]" id="humidity_alarm<?php echo $i;?>" style="width: 100px;">

                <?php
                $alarms = $alarmdirs[0]->alarms;
                foreach($alarmdirs as $i1=>$item1)
                {
                    if($item1->id == $item->humidity_alarmdir)
                    {
                      $alarms = $item1->alarms;
                      break;
                    }
                }
                ?>

                <?php foreach($alarms as $i1=>$item1): ?>
                <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->humidity_alarm) echo("selected=selected");?>><?php echo $item1->name;?></option>
              <?php endforeach;?>
            </select>
          </span>
          </br>

          CO連動閾值
          <input type="number" style="width:80px;" name="co_sensor_alarm_threshold[]" value="<?php echo $item->co_sensor_alarm_threshold;?>"/>
          CO連動資料夾
          <select name="co_sensor_alarmdir[]" style="width: 100px;" onchange="co_sensorAlarmChange(this,<?php echo$i;?>)">
              <?php foreach($alarmdirs as $i1=>$item1): ?>
              <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->co_sensor_alarmdir) echo("selected=selected");?>><?php echo $item1->name;?></option>
            <?php endforeach;?>
          </select>
          
          CO連動
          <span id="co_sensor_alarm_span<?php echo $i;?>">
              <select name="co_sensor_alarm[]" id="co_sensor_alarm<?php echo $i;?>" style="width: 100px;">

                <?php
                $alarms = $alarmdirs[0]->alarms;
                foreach($alarmdirs as $i1=>$item1)
                {
                    if($item1->id == $item->co_sensor_alarmdir)
                    {
                      $alarms = $item1->alarms;
                      break;
                    }
                }
                ?>

                <?php foreach($alarms as $i1=>$item1): ?>
                <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->co_sensor_alarm) echo("selected=selected");?>><?php echo $item1->name;?></option>
              <?php endforeach;?>
            </select>
          </span>
          </br>
          <?php endif; ?>
          CO2連動閾值
          <input type="number" style="width:80px;" name="co2_ppm_alarm_threshold[]" value="<?php echo $item->co2_ppm_alarm_threshold;?>"/>
          CO2連動資料夾
          <select name="co2_ppm_alarmdir[]" style="width: 100px;" onchange="co2_ppmAlarmChange(this,<?php echo$i;?>)">
              <?php foreach($alarmdirs as $i1=>$item1): ?>
              <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->co2_ppm_alarmdir) echo("selected=selected");?>><?php echo $item1->name;?></option>
            <?php endforeach;?>
          </select>
          
          CO2連動
          <span id="co2_ppm_alarm_span<?php echo $i;?>">
              <select name="co2_ppm_alarm[]" id="co2_ppm_alarm<?php echo $i;?>" style="width: 100px;">

                <?php
                $alarms = $alarmdirs[0]->alarms;
                foreach($alarmdirs as $i1=>$item1)
                {
                    if($item1->id == $item->co2_ppm_alarmdir)
                    {
                      $alarms = $item1->alarms;
                      break;
                    }
                }
                ?>

                <?php foreach($alarms as $i1=>$item1): ?>
                <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->co2_ppm_alarm) echo("selected=selected");?>><?php echo $item1->name;?></option>
              <?php endforeach;?>
            </select>
          </span>
          </br>
          <?php if ($this->items[0]->dio_type != TopHelpersTop::$dio_temp_humidity_pinron_dio_type): ?>
          TVOC連動閾值
          <input type="number" style="width:80px;" name="tvoc_alarm_threshold[]" value="<?php echo $item->tvoc_alarm_threshold;?>"/>
          TVOC連動資料夾
          <select name="tvoc_alarmdir[]" style="width: 100px;" onchange="tvocAlarmChange(this,<?php echo$i;?>)">
              <?php foreach($alarmdirs as $i1=>$item1): ?>
              <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->tvoc_alarmdir) echo("selected=selected");?>><?php echo $item1->name;?></option>
            <?php endforeach;?>
          </select>
          
          TVOC連動
          <span id="tvoc_alarm_span<?php echo $i;?>">
              <select name="tvoc_alarm[]" id="tvoc_alarm<?php echo $i;?>" style="width: 100px;">

                <?php
                $alarms = $alarmdirs[0]->alarms;
                foreach($alarmdirs as $i1=>$item1)
                {
                    if($item1->id == $item->tvoc_alarmdir)
                    {
                      $alarms = $item1->alarms;
                      break;
                    }
                }
                ?>

                <?php foreach($alarms as $i1=>$item1): ?>
                <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->tvoc_alarm) echo("selected=selected");?>><?php echo $item1->name;?></option>
              <?php endforeach;?>
            </select>
          </span>
          </br>

          HCHO連動閾值
          <input type="number" style="width:80px;" name="hcho_alarm_threshold[]" value="<?php echo $item->hcho_alarm_threshold;?>"/>
          HCHO連動資料夾
          <select name="hcho_alarmdir[]" style="width: 100px;" onchange="hchoAlarmChange(this,<?php echo$i;?>)">
              <?php foreach($alarmdirs as $i1=>$item1): ?>
              <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->hcho_alarmdir) echo("selected=selected");?>><?php echo $item1->name;?></option>
            <?php endforeach;?>
          </select>
          
          HCHO連動
          <span id="hcho_alarm_span<?php echo $i;?>">
              <select name="hcho_alarm[]" id="hcho_alarm<?php echo $i;?>" style="width: 100px;">

                <?php
                $alarms = $alarmdirs[0]->alarms;
                foreach($alarmdirs as $i1=>$item1)
                {
                    if($item1->id == $item->hcho_alarmdir)
                    {
                      $alarms = $item1->alarms;
                      break;
                    }
                }
                ?>

                <?php foreach($alarms as $i1=>$item1): ?>
                <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->hcho_alarm) echo("selected=selected");?>><?php echo $item1->name;?></option>
              <?php endforeach;?>
            </select>
          </span>
          </br>

          PM1.0連動閾值
          <input type="number" style="width:80px;" name="pm01_alarm_threshold[]" value="<?php echo $item->pm01_alarm_threshold;?>"/>
          PM1.0連動資料夾
          <select name="pm01_alarmdir[]" style="width: 100px;" onchange="pm01AlarmChange(this,<?php echo$i;?>)">
              <?php foreach($alarmdirs as $i1=>$item1): ?>
              <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->pm01_alarmdir) echo("selected=selected");?>><?php echo $item1->name;?></option>
            <?php endforeach;?>
          </select>
          
          PM1.0連動
          <span id="pm01_alarm_span<?php echo $i;?>">
              <select name="pm01_alarm[]" id="pm01_alarm<?php echo $i;?>" style="width: 100px;">

                <?php
                $alarms = $alarmdirs[0]->alarms;
                foreach($alarmdirs as $i1=>$item1)
                {
                    if($item1->id == $item->pm01_alarmdir)
                    {
                      $alarms = $item1->alarms;
                      break;
                    }
                }
                ?>

                <?php foreach($alarms as $i1=>$item1): ?>
                <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->pm01_alarm) echo("selected=selected");?>><?php echo $item1->name;?></option>
              <?php endforeach;?>
            </select>
          </span>
          </br>

          PM2.5連動閾值
          <input type="number" style="width:80px;" name="pm25_alarm_threshold[]" value="<?php echo $item->pm25_alarm_threshold;?>"/>
          PM2.5連動資料夾
          <select name="pm25_alarmdir[]" style="width: 100px;" onchange="pm25AlarmChange(this,<?php echo$i;?>)">
              <?php foreach($alarmdirs as $i1=>$item1): ?>
              <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->pm25_alarmdir) echo("selected=selected");?>><?php echo $item1->name;?></option>
            <?php endforeach;?>
          </select>
          
          PM2.5連動
          <span id="pm25_alarm_span<?php echo $i;?>">
              <select name="pm25_alarm[]" id="pm25_alarm<?php echo $i;?>" style="width: 100px;">

                <?php
                $alarms = $alarmdirs[0]->alarms;
                foreach($alarmdirs as $i1=>$item1)
                {
                    if($item1->id == $item->pm25_alarmdir)
                    {
                      $alarms = $item1->alarms;
                      break;
                    }
                }
                ?>

                <?php foreach($alarms as $i1=>$item1): ?>
                <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->pm25_alarm) echo("selected=selected");?>><?php echo $item1->name;?></option>
              <?php endforeach;?>
            </select>
          </span>
          </br>

          PM10連動閾值
          <input type="number" style="width:80px;" name="pm10_alarm_threshold[]" value="<?php echo $item->pm10_alarm_threshold;?>"/>
          PM10連動資料夾
          <select name="pm10_alarmdir[]" style="width: 100px;" onchange="pm10AlarmChange(this,<?php echo$i;?>)">
              <?php foreach($alarmdirs as $i1=>$item1): ?>
              <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->pm10_alarmdir) echo("selected=selected");?>><?php echo $item1->name;?></option>
            <?php endforeach;?>
          </select>
          
          PM10連動
          <span id="pm10_alarm_span<?php echo $i;?>">
              <select name="pm10_alarm[]" id="pm10_alarm<?php echo $i;?>" style="width: 100px;">

                <?php
                $alarms = $alarmdirs[0]->alarms;
                foreach($alarmdirs as $i1=>$item1)
                {
                    if($item1->id == $item->pm10_alarmdir)
                    {
                      $alarms = $item1->alarms;
                      break;
                    }
                }
                ?>

                <?php foreach($alarms as $i1=>$item1): ?>
                <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->pm10_alarm) echo("selected=selected");?>><?php echo $item1->name;?></option>
              <?php endforeach;?>
            </select>
          </span>
          </br>
          <?php endif; ?>
          <?php if ($this->items[0]->dio_type == TopHelpersTop::$dio_jnc_temp_humidity_dio_type): ?>
              CO2連動閾值
              <input type="number" style="width:80px;" name="co2_ppm_alarm_threshold[]" value="<?php echo $item->co2_ppm_alarm_threshold;?>"/>
              CO2連動資料夾
              <select name="co2_ppm_alarmdir[]" style="width: 100px;" onchange="co2_ppmAlarmChange(this,<?php echo$i;?>)">
                  <?php foreach($alarmdirs as $i1=>$item1): ?>
                  <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->co2_ppm_alarmdir) echo("selected=selected");?>><?php echo $item1->name;?></option>
                <?php endforeach;?>
              </select>
              <!-- <input type="number" style="width:80px;" name="co2_ppm_alarmdir[]" value="<?php echo $item->co2_ppm_alarmdir;?>"/> -->
              CO2連動
              <span id="co2_ppm_alarm_span<?php echo $i;?>">
                  <select name="co2_ppm_alarm[]" id="co2_ppm_alarm<?php echo $i;?>" style="width: 100px;">

                    <?php
                    $alarms = $alarmdirs[0]->alarms;
                    foreach($alarmdirs as $i1=>$item1)
                    {
                        if($item1->id == $item->co2_ppm_alarmdir)
                        {
                          $alarms = $item1->alarms;
                          break;
                        }
                    }
                    ?>

                    <?php foreach($alarms as $i1=>$item1): ?>
                    <option value="<?php echo $item1->id;?>" <?php if($item1->id==$item->co2_ppm_alarm) echo("selected=selected");?>><?php echo $item1->name;?></option>
                  <?php endforeach;?>
                </select>
              </span>
              </br>
          <?php endif; ?>
          <!-- <input type="number" style="width:80px;" name="humidity_alarm[]" value="<?php echo $item->humidity_alarm;?>"/> -->
        </td>
      </tr>
             
    <?php else: ?>
       <input type="hidden" name="temperature_alarm_threshold[]" value="<?php echo $item->temperature_alarm_threshold;?>"/>
        <input type="hidden" name="temperature_alarmdir[]" value="<?php echo $item->temperature_alarmdir;?>"/>
        <input type="hidden" name="temperature_alarm[]" value="<?php echo $item->temperature_alarm;?>"/>
        
        <input type="hidden" name="humidity_alarm_threshold[]" value="<?php echo $item->humidity_alarm_threshold;?>"/>
        <input type="hidden" name="humidity_alarmdir[]" value="<?php echo $item->humidity_alarmdir;?>"/>
        <input type="hidden" name="humidity_alarm[]" value="<?php echo $item->humidity_alarm;?>"/>

        <input type="hidden" name="co2_ppm_alarm_threshold[]" value="<?php echo $item->co2_ppm_alarm_threshold;?>"/>
        <input type="hidden" name="co2_ppm_alarmdir[]" value="<?php echo $item->co2_ppm_alarmdir;?>"/>
        <input type="hidden" name="co2_ppm_alarm[]" value="<?php echo $item->co2_ppm_alarm;?>"/>
        
        <input type="hidden" name="co_sensor_alarm_threshold[]" value="<?php echo $item->co_sensor_alarm_threshold;?>"/>
        <input type="hidden" name="co_sensor_alarmdir[]" value="<?php echo $item->co_sensor_alarmdir;?>"/>
        <input type="hidden" name="co_sensor_alarm[]" value="<?php echo $item->co_sensor_alarm;?>"/>
        
        <input type="hidden" name="tvoc_alarm_threshold[]" value="<?php echo $item->tvoc_alarm_threshold;?>"/>
        <input type="hidden" name="tvoc_alarmdir[]" value="<?php echo $item->tvoc_alarmdir;?>"/>
        <input type="hidden" name="tvoc_alarm[]" value="<?php echo $item->tvoc_alarm;?>"/>
        
        <input type="hidden" name="hcho_alarm_threshold[]" value="<?php echo $item->hcho_alarm_threshold;?>"/>
        <input type="hidden" name="hcho_alarmdir[]" value="<?php echo $item->hcho_alarmdir;?>"/>
        <input type="hidden" name="hcho_alarm[]" value="<?php echo $item->hcho_alarm;?>"/>
        
        <input type="hidden" name="pm01_alarm_threshold[]" value="<?php echo $item->pm01_alarm_threshold;?>"/>
        <input type="hidden" name="pm01_alarmdir[]" value="<?php echo $item->pm01_alarmdir;?>"/>
        <input type="hidden" name="pm01_alarm[]" value="<?php echo $item->pm01_alarm;?>"/>
        
        <input type="hidden" name="pm25_alarm_threshold[]" value="<?php echo $item->pm25_alarm_threshold;?>"/>
        <input type="hidden" name="pm25_alarmdir[]" value="<?php echo $item->pm25_alarmdir;?>"/>
        <input type="hidden" name="pm25_alarm[]" value="<?php echo $item->pm25_alarm;?>"/>
        
        <input type="hidden" name="pm10_alarm_threshold[]" value="<?php echo $item->pm10_alarm_threshold;?>"/>
        <input type="hidden" name="pm10_alarmdir[]" value="<?php echo $item->pm10_alarmdir;?>"/>
        <input type="hidden" name="pm10_alarm[]" value="<?php echo $item->pm10_alarm;?>"/>

        <input type="hidden" name="lux_alarm_threshold[]" value="<?php echo $item->lux_alarm_threshold;?>"/>
        <input type="hidden" name="lux_alarmdir[]" value="<?php echo $item->lux_alarmdir;?>"/>
        <input type="hidden" name="lux_alarm[]" value="<?php echo $item->lux_alarm;?>"/>
    <?php endif; ?>

     
    <input type="hidden" name="id1[]" value="<?php echo $item->id;?>"/>
    <input type="hidden" name="index[]" value="<?php echo $item->index;?>"/>

  <?php endforeach;?>
  </table>
<?php endif;?>
</div></div>
	<input type="hidden" name="task" value=""/>
	<input type="hidden" name="boxchecked" value="0"/>
  <input type="hidden" name="id" value="<?php echo $myid;?>"/>
  <input type="hidden" name="is_save" id="is_save" value="0"/>
	<input type="hidden" name="filter_order" value="<?php echo $listOrder; ?>"/>
	<input type="hidden" name="filter_order_Dir" value="<?php echo $listDirn; ?>"/>
	<?php echo HTMLHelper::_('form.token'); ?>
</form>

<?php if($canDelete) : ?>
<script type="text/javascript">


var m_Obj = [
<?php foreach($top_floors as $i=>$item):?>
  [
  <?php foreach($item->floors as $i1=>$item1):?>
  {
    building:<?php echo $item->id;?>,
   id:<?php echo $item1->id?>,
   name:"<?php echo $item1->name?>"
  },
  <?php endforeach;?>
  ],
  <?php endforeach;?>
];

var m_alarmObj = [
<?php foreach($alarmdirs as $i=>$item):?>
  [
  <?php foreach($item->alarms as $i1=>$item1):?>
  {
    alarmdir:<?php echo $item->id;?>,
   id:<?php echo $item1->id?>,
   name:"<?php echo $item1->name?>"
  },
  <?php endforeach;?>
  ],
  <?php endforeach;?>
];

var home_timeout = 1000*120;
var m_index=0;
function gohome() {
  ///window.location.href = '<?php echo JRoute::_('index.php?option=com_whome&view=roots'); ?>';

}

	jQuery(document).ready(function () {
		jQuery('.delete-button').click(deleteItem);
    jQuery("#save1").click(saveItem);
    jQuery("#close1").click(closeItem);

    <?php foreach($this->devices as $i=>$item):?>
    <?php if($item->supergroup == 0):?>
    //jQuery("#group_span<?php echo $i?>").hide();
    //jQuery("#top_floor_span<?php echo $i?>").hide();
    //jQuery("#myspan<?php echo $i?>").hide();

    //jQuery("#dio_alarmdir_span<?php echo $i?>").hide();
    <?php endif;?>
    <?php endforeach;?>

    //console.log(m_alarmObj);
    setTimeout(gohome, home_timeout);
	});




  function closeItem() {

	//alert('333333');
       window.location.href = '<?php echo JRoute::_('index.php?option=com_top&view=ddiopage&id='.(int) $top_id.'&group='.(int) $top_id, false, 2) ?>';

  }

  function saveItem() {

	    //alert('1111');
	    form = document.getElementById('adminForm');

      myname = document.getElementById('name');

      if(myname.value.length == 0)
      {
        alert('empty name');
        return;
      }

      myname1 = myname.value.replace(/\s/g,'');
      if(myname1.length == 0)
      {
        alert('all space');
        return;
      }

      <?php if($this->items[0]->vendor == 1):?>
      if(ValidateIPaddress(myname1)==false)
      {
        return;
      }
      <?php endif;?>
      count = <?php echo(count($this->devices));?>;
      jQuery('.show_node_text_checkbox').each((index,checkbox) => {
        console.log(checkbox);
        var hidden = jQuery(checkbox).siblings('.show_node_text_hidden');        
        if (jQuery(checkbox).prop('checked'))
        {
          console.log('checked');
          jQuery(hidden).val('1');
        }
        else
        {
          console.log('unchecked');
          jQuery(hidden).val('0');
        }
      });     
      jQuery('.trigger_alarm_checkbox').each((index,checkbox) => {
        console.log(checkbox);
        var hidden = jQuery(checkbox).siblings('.trigger_alarm_hidden');        
        if (jQuery(checkbox).prop('checked'))
        {
          console.log('checked');
          jQuery(hidden).val('1');
        }
        else
        {
          console.log('unchecked');
          jQuery(hidden).val('0');
        }
      });      
      jQuery('.is_fault_node_checkbox').each((index,checkbox) => {
        console.log(checkbox);
        var hidden = jQuery(checkbox).siblings('.is_fault_node_hidden');        
        if (jQuery(checkbox).prop('checked'))
        {
          console.log('checked');
          jQuery(hidden).val('1');
        }
        else
        {
          console.log('unchecked');
          jQuery(hidden).val('0');
        }
      });      
      jQuery('.display_last_alarm_time_checkbox').each((index,checkbox) => {
        console.log(checkbox);
        var hidden = jQuery(checkbox).siblings('.display_last_alarm_time_hidden');        
        if (jQuery(checkbox).prop('checked'))
        {
          console.log('checked');
          jQuery(hidden).val('1');
        }
        else
        {
          console.log('unchecked');
          jQuery(hidden).val('0');
        }
      });      

      jQuery('.ba_app_enable_checkbox').each((index,checkbox) => {
        console.log(checkbox);
        var hidden = jQuery(checkbox).siblings('.ba_app_enable_hidden');        
        if (jQuery(checkbox).prop('checked'))
        {
          console.log('checked');
          jQuery(hidden).val('1');
        }
        else
        {
          console.log('unchecked');
          jQuery(hidden).val('0');
        }
      });    
      for(var i=0;i<count;i++)
      {
          ele = document.getElementById("enable"+i);

          if(ele != null)
          {
              if(ele.checked != false)
                  ele.value = 1;
              else
                  ele.value = 0;

              ele1 = document.getElementById("myenable"+i);

              ele1.value = ele.value;

          }

      }

      form.is_save.value = 1;

        // Submit the form.
        if (typeof form.onsubmit == 'function') {
            form.onsubmit();
        }
        if (typeof form.fireEvent == "function") {
            form.fireEvent('onsubmit');
        }

        //Joomla.submitbutton('roots.add1');
        form.submit();

  }

  function ValidateIPaddress(ipaddress)
  {
   if (/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(ipaddress))
    {
      return (true)
    }
    alert("You have entered an invalid IP address")
    return (false)
  }
	function deleteItem() {

		if (!confirm("<?php echo Text::_('COM_TOP_DELETE_MESSAGE'); ?>")) {
			return false;
		}
	}

  function myChange(selectObject,index) {

      jQuery("#myspan"+index).empty();

      var obj='<select name="dio_floor[]"id="dio_floor'+index+'" style="width: 100px;">';

      //var x = document.getElementById("top_floor"+index);

      for(i=0;i<m_Obj.length;i++)
      {
          if(m_Obj[i][0].building == selectObject.value)
          {
            for(j=0;j<m_Obj[i].length;j++)
            {
               obj=obj+'<option value='+m_Obj[i][j].id+'>'+m_Obj[i][j].name+'</option>';
               //option.value = m_Obj[i][j].id;
            }
            break;
          }
      }

      obj=obj+'</select>';

      jQuery("#myspan"+index).html(obj);

  }

  function groupChange(selectObject,index) {
      m_index = index;
      groupFunc(selectObject);
      return;
      if(selectObject.value == 0)
      {
          jQuery("#group_span"+index).hide();
          jQuery("#top_floor_span"+index).hide();
          jQuery("#myspan"+index).hide();

          //jQuery("#dio_alarmdir_span"+index).hide();
      }
      else
      {
        jQuery("#group_span"+index).show();
        jQuery("#top_floor_span"+index).show();
        jQuery("#myspan"+index).show();

        //jQuery("#dio_alarmdir_span"+index).show();

      }
  }
  function tvocAlarmChange (selectObject,index) {

    jQuery("#tvoc_alarm_span"+index).empty();

    var obj='<select name="tvoc_alarm[]" id="tvoc_alarm'+index+'" style="width: 100px;">';

    //console.log(selectObject.value);
    for(i=0;i<m_alarmObj.length;i++)
    {
        //console.log(m_alarmObj[i][0].id);
        if(m_alarmObj[i][0].alarmdir == selectObject.value)
        {

          for(j=0;j<m_alarmObj[i].length;j++)
          {
            obj=obj+'<option value='+m_alarmObj[i][j].id+'>'+m_alarmObj[i][j].name+'</option>';
            //option.value = m_Obj[i][j].id;
          }
          break;
        }
    }

    obj=obj+'</select>';

    console.log(obj);
    jQuery("#tvoc_alarm_span"+index).html(obj);
  }  
  function hchoAlarmChange (selectObject,index) {

    jQuery("#hcho_alarm_span"+index).empty();

    var obj='<select name="hcho_alarm[]" id="hcho_alarm'+index+'" style="width: 100px;">';

    //console.log(selectObject.value);
    for(i=0;i<m_alarmObj.length;i++)
    {
        //console.log(m_alarmObj[i][0].id);
        if(m_alarmObj[i][0].alarmdir == selectObject.value)
        {

          for(j=0;j<m_alarmObj[i].length;j++)
          {
            obj=obj+'<option value='+m_alarmObj[i][j].id+'>'+m_alarmObj[i][j].name+'</option>';
            //option.value = m_Obj[i][j].id;
          }
          break;
        }
    }

    obj=obj+'</select>';

    console.log(obj);
    jQuery("#hcho_alarm_span"+index).html(obj);
  }

  function pm10AlarmChange (selectObject,index) {

    jQuery("#pm10_alarm_span"+index).empty();

    var obj='<select name="pm10_alarm[]" id="pm10_alarm'+index+'" style="width: 100px;">';

    //console.log(selectObject.value);
    for(i=0;i<m_alarmObj.length;i++)
    {
        //console.log(m_alarmObj[i][0].id);
        if(m_alarmObj[i][0].alarmdir == selectObject.value)
        {

          for(j=0;j<m_alarmObj[i].length;j++)
          {
            obj=obj+'<option value='+m_alarmObj[i][j].id+'>'+m_alarmObj[i][j].name+'</option>';
            //option.value = m_Obj[i][j].id;
          }
          break;
        }
    }

    obj=obj+'</select>';

    console.log(obj);
    jQuery("#pm10_alarm_span"+index).html(obj);
  }  
  function pm25AlarmChange (selectObject,index) {

    jQuery("#pm25_alarm_span"+index).empty();

    var obj='<select name="pm25_alarm[]" id="pm25_alarm'+index+'" style="width: 100px;">';

    //console.log(selectObject.value);
    for(i=0;i<m_alarmObj.length;i++)
    {
        //console.log(m_alarmObj[i][0].id);
        if(m_alarmObj[i][0].alarmdir == selectObject.value)
        {

          for(j=0;j<m_alarmObj[i].length;j++)
          {
            obj=obj+'<option value='+m_alarmObj[i][j].id+'>'+m_alarmObj[i][j].name+'</option>';
            //option.value = m_Obj[i][j].id;
          }
          break;
        }
    }

    obj=obj+'</select>';

    console.log(obj);
    jQuery("#pm25_alarm_span"+index).html(obj);
  }  
  function pm01AlarmChange (selectObject,index) {

    jQuery("#pm01_alarm_span"+index).empty();

    var obj='<select name="pm01_alarm[]" id="pm01_alarm'+index+'" style="width: 100px;">';

    //console.log(selectObject.value);
    for(i=0;i<m_alarmObj.length;i++)
    {
        //console.log(m_alarmObj[i][0].id);
        if(m_alarmObj[i][0].alarmdir == selectObject.value)
        {

          for(j=0;j<m_alarmObj[i].length;j++)
          {
            obj=obj+'<option value='+m_alarmObj[i][j].id+'>'+m_alarmObj[i][j].name+'</option>';
            //option.value = m_Obj[i][j].id;
          }
          break;
        }
    }

    obj=obj+'</select>';

    console.log(obj);
    jQuery("#pm01_alarm_span"+index).html(obj);
  }  
  function co_sensorAlarmChange (selectObject,index) {

    jQuery("#co_sensor_alarm_span"+index).empty();

    var obj='<select name="co_sensor_alarm[]" id="co_sensor_alarm'+index+'" style="width: 100px;">';

    //console.log(selectObject.value);
    for(i=0;i<m_alarmObj.length;i++)
    {
        //console.log(m_alarmObj[i][0].id);
        if(m_alarmObj[i][0].alarmdir == selectObject.value)
        {

          for(j=0;j<m_alarmObj[i].length;j++)
          {
            obj=obj+'<option value='+m_alarmObj[i][j].id+'>'+m_alarmObj[i][j].name+'</option>';
            //option.value = m_Obj[i][j].id;
          }
          break;
        }
    }

    obj=obj+'</select>';

    console.log(obj);
    jQuery("#co_sensor_alarm_span"+index).html(obj);
  }
  function co2_ppmAlarmChange (selectObject,index) {

    jQuery("#co2_ppm_alarm_span"+index).empty();

    var obj='<select name="co2_ppm_alarm[]" id="co2_ppm_alarm'+index+'" style="width: 100px;">';

    //console.log(selectObject.value);
    for(i=0;i<m_alarmObj.length;i++)
    {
        //console.log(m_alarmObj[i][0].id);
        if(m_alarmObj[i][0].alarmdir == selectObject.value)
        {

          for(j=0;j<m_alarmObj[i].length;j++)
          {
            obj=obj+'<option value='+m_alarmObj[i][j].id+'>'+m_alarmObj[i][j].name+'</option>';
            //option.value = m_Obj[i][j].id;
          }
          break;
        }
    }

    obj=obj+'</select>';

    console.log(obj);
    jQuery("#co2_ppm_alarm_span"+index).html(obj);
  }
  function temperatureAlarmChange(selectObject,index) {

    jQuery("#temperature_alarm_span"+index).empty();

    var obj='<select name="temperature_alarm[]" id="temperature_alarm'+index+'" style="width: 100px;">';

    //console.log(selectObject.value);
    for(i=0;i<m_alarmObj.length;i++)
    {
        //console.log(m_alarmObj[i][0].id);
        if(m_alarmObj[i][0].alarmdir == selectObject.value)
        {

          for(j=0;j<m_alarmObj[i].length;j++)
          {
            obj=obj+'<option value='+m_alarmObj[i][j].id+'>'+m_alarmObj[i][j].name+'</option>';
            //option.value = m_Obj[i][j].id;
          }
          break;
        }
    }

    obj=obj+'</select>';

    console.log(obj);
    jQuery("#temperature_alarm_span"+index).html(obj);
  }
  function luxAlarmChange(selectObject,index) {

    jQuery("#lux_alarm_span"+index).empty();

    var obj='<select name="lux_alarm[]" id="lux_alarm'+index+'" style="width: 100px;">';

    //console.log(selectObject.value);
    for(i=0;i<m_alarmObj.length;i++)
    {
        //console.log(m_alarmObj[i][0].id);
        if(m_alarmObj[i][0].alarmdir == selectObject.value)
        {

          for(j=0;j<m_alarmObj[i].length;j++)
          {
            obj=obj+'<option value='+m_alarmObj[i][j].id+'>'+m_alarmObj[i][j].name+'</option>';
            //option.value = m_Obj[i][j].id;
          }
          break;
        }
    }

    obj=obj+'</select>';

    console.log(obj);
    jQuery("#lux_alarm_span"+index).html(obj);
  }
  function humidityAlarmChange(selectObject,index) {

    jQuery("#humidity_alarm_span"+index).empty();

    var obj='<select name="humidity_alarm[]" id="humidity_alarm'+index+'" style="width: 100px;">';

    //console.log(selectObject.value);
    for(i=0;i<m_alarmObj.length;i++)
    {
        //console.log(m_alarmObj[i][0].id);
        if(m_alarmObj[i][0].alarmdir == selectObject.value)
        {

          for(j=0;j<m_alarmObj[i].length;j++)
          {
            obj=obj+'<option value='+m_alarmObj[i][j].id+'>'+m_alarmObj[i][j].name+'</option>';
            //option.value = m_Obj[i][j].id;
          }
          break;
        }
    }

    obj=obj+'</select>';

    console.log(obj);
    jQuery("#humidity_alarm_span"+index).html(obj);
  }
  function alarmChange(selectObject,index) {

    jQuery("#dio_alarm_span"+index).empty();

    var obj='<select name="dio_alarm'+index +'" id="dio_alarm'+index+'" style="width: 100px;">';

    //console.log(selectObject.value);
    for(i=0;i<m_alarmObj.length;i++)
    {
        //console.log(m_alarmObj[i][0].id);
        if(m_alarmObj[i][0].alarmdir == selectObject.value)
        {

          for(j=0;j<m_alarmObj[i].length;j++)
          {
             obj=obj+'<option value='+m_alarmObj[i][j].id+'>'+m_alarmObj[i][j].name+'</option>';
             //option.value = m_Obj[i][j].id;
          }
          break;
        }
    }

    obj=obj+'</select>';

    console.log(obj);
    jQuery("#dio_alarm_span"+index).html(obj);
  }

  function groupFunc(obj) {

    alprUrl = "<?php echo JRoute::_('index.php?option=com_top&task=page.getType'); ?>";


    var myvar = {id:obj.value};

    var jsonArray = JSON.stringify(myvar);
    console.log(jsonArray);
    jQuery.ajax({

        url: alprUrl,
        headers: {
            "Content-Type": "application/json"
        },
        type: "POST",
        data: jsonArray,
        success: function(response) {
            //console.log(response);

            var arr = JSON.parse(response);
            changeType(arr);

            //setTimeout(statusFunc, timeout);
        },
        error: function(response) {
            console.log("error4");
            alert('Error!');
            //setTimeout(statusFunc, timeout*10);

        }
    });

  }
  function changeType(arr) {

    jQuery("#group_span"+m_index).empty();

    var obj='<select name="group[]" style="width: 100px;" >';

    jQuery.each(arr, function(index, value) {

      obj = obj+'<option value="'+value.id+'" >'+value.name+'</option>';


    });

    obj=obj+'</select>';

    jQuery("#group_span"+m_index).html(obj);

    console.log(obj);
    //typeFunc();
  }
</script>
<?php endif; ?>
