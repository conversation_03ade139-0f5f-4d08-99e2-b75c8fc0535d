#include "modbusDev.hpp"
#include "utility.hpp"
#include "crc.h"
using namespace std;
modbusDev::modbusDev()
{
  device_id = 0;
  DI_addr_start = 0;
  DI_count = 0;

  DO_addr_start = 0;
  DO_count = 0;
  this->appendCrc = false;
  this->useCoilForDi = false;
  state = EState::GET_DI_STATE;
  dio_type = 1;
}
modbusDev:: modbusDev(bool appendCrc, bool useCoilForDi)
{
  modbusDev();
  this->appendCrc = appendCrc;
  this->useCoilForDi = useCoilForDi;
}
modbusDev::~modbusDev()
{
    //cout<<"\nmodbusDev::"<<__func__<<endl;
}

int modbusDev::set(Json::Value it)
{
  set_int_value(it,&device_id,"device_id");
  set_int_value(it,&dio_type,"dio_type");
  int index = 0;
  set_int_value(it,&index,"header_id");
  uint16_t header_id = index;
  //cout<<"\n"<<it<<endl;

  if(dio_type == 1)
  {
    doAddDIDevice(it);
  }
  else if(dio_type == 2)
  {
    doAddDODevice(it);
  }
  else
  {
    doAddDODevice(it);

  }

  auto it1 = it["nodes"];
  for(auto it2 : it1)
  {
      it2["pid"] = it["pid"];
      it2["did"] = it["id"];
      it2["addr"] = it["addr"];
      it2["port"] = it["port"];
      it2["pdio_type"] = it["dio_type"];
      it2["header_id"] = header_id;
      if(it2["enable"] == "1")
      {
          auto dev = std::make_shared<modbusNode>();// modbusTCPDev dev;
          dev->set(it2);
          //std::cout << "elm: " << it ;//<< std::endl;

          nodes.push_back(dev);
      }
  }

  init_iter();


  return nodes.size();
}
EState modbusDev::getEState()
{
  return state;
}
void modbusDev::init_iter()
{
    iterator = nodes.begin();
    state = EState::GET_DI_STATE;

    for(auto& node : nodes)
    {
      node->init_iter();
    }

}
bool modbusDev::is_analog_device()
{
     if(dio_type == TEMP_DEVICE ||
        dio_type == ELEC_DEVICE ||
        dio_type == CO_SENSOR_DEVICE ||
        dio_type == WATER_METER_DEVICE ||
        dio_type == SMART_LAMP_DEVICE ||
        dio_type == CIC_ELEC_DEVICE ||
        dio_type == ACUVIM_ELEC_DEVICE ||
        dio_type == TATUNG_ELEC_DEVICE ||
        dio_type == YON_GJIA_TEMP_DEVICE_WITH_LUX ||
        dio_type == YON_GJIA_TEMP_DEVICE ||
        dio_type == YON_GJIA_TEMP2_DEVICE ||
        dio_type == YON_GJIA_TEMP_3_IN_1_DEVICE ||
        dio_type == CO_SENSOR_YON_GJIA_DEVICE ||
        dio_type == PM_SENSOR_YON_GJIA_DEVICE ||
        dio_type == BENDER_PEM333_ELEC_DEVICE ||
        dio_type == BENDER_PEM575_ELEC_DEVICE ||
        dio_type == SHIHLIN_ELEC_DEVICE ||
        dio_type == JETEC_WIND_DIRECTION_DEVICE ||
        dio_type == JETEC_SOIL_METER_DEVICE ||
        dio_type == JETEC_RAIN_METER_DEVICE ||
        dio_type == JNC_TEMP_DEVICE ||
        dio_type == PRIMEVOLT_SOLAR_POWER_METER_DEVICE ||
        dio_type == TKD_WATER_METER_DEVICE ||
        dio_type == VMR_MP7_ELEC_DEVICE ||
        dio_type == CO2_DEVICE ||
        dio_type == SHALUN_OPCDA_ELEC_DEVICE ||
        dio_type == GENERAL_OPCDA_ELEC_DEVICE ||
        dio_type == GENERAL_OPCDA_WATER_DEVICE ||
        dio_type == WEEMA_ELEC_DEVICE_1P ||
        dio_type == WEEMA_ELEC_DEVICE_3P ||
        dio_type == IRTI_IVA_PERSON_COUNTING_NODE ||
        dio_type == IRTI_IVA_PERSON_DETECTION_NODE ||
        dio_type == WEEMA_IAQ_NODE ||
        dio_type == AEM_DRB_ELEC_DEVICE ||
        dio_type == PINRON_TEMP_DEVICE ||
        dio_type == SHIHLIN_SPM8_SOLAR_POWER_METER_DEVICE ||
        dio_type == CO_SENSOR_GENERAL_DEVICE ||
        dio_type == GENERAL_AI_DEVICE ||
        dio_type == GENERAL_SOLAR_DEVICE ||
        dio_type == CIC_BAW1A2A_ELEC_DEVICE
        )
     {
       return true;
     }
     else
     {
       return false;
     }
}

bool modbusDev::check_iter_valid()
{
  bool is_find = false;

  auto it = nodes.begin();

  for(;it != nodes.end();it++)
  {
    if(it == iterator)
    {
      is_find = true;
      break;
    }
  }

  return is_find;


}

bool modbusDev::set_data(uint8_t* p_data,int len)
{
  bool ret;

  ret = false;
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;

  for(auto& node : nodes)
  {
    if(node->set_data(p_data,len))
    {
      ret = true;
      break;
    }
  }

  return ret;
}

bool modbusDev::analog_next_data(uint8_t*p_buf,int*p_len)
{
  if(nodes.size() == 0)    return false;

  if(!check_iter_valid())
  {
    iterator = nodes.begin();
  }

  bool ret;

  ret = (*iterator)->get_next_data(p_buf,p_len);

  if(ret == false)
  {
    iterator++;
    if(iterator == nodes.end())
    {
      iterator = nodes.begin();
    }
    else
    {

      ret = (*iterator)->get_next_data(p_buf,p_len);

    }
  }


  return ret;

}
bool modbusDev::get_next_data(uint8_t*p_buf,int*p_len)
{
  if(is_analog_device())
  {
      return analog_next_data(p_buf,p_len);
  }
  else if(state == EState::GET_DI_STATE)
  {
      state = EState::GET_DO_STATE;
      bool rt = get_di_data(p_buf,p_len);
      if (rt) return true;      
  }
  if(state == EState::GET_DO_STATE)
  {
    state = EState::INVALID_STATE;
    return get_do_data(p_buf,p_len);

  }
  else if(state == EState::INVALID_STATE)
  {
    state = EState::GET_DI_STATE;
    return false;
  }
  return true;
}
void modbusDev::doAddDIDevice(Json::Value it)
{
    int addr;
    set_int_value(it,&addr,"addr");

    int dio_port;
    set_int_value(it,&dio_port,"port");

    int port;

    port = (addr-DI_addr_start)+dio_port;

    if(DI_addr_start == 0)
    {
       DI_addr_start = addr;
       DI_count = dio_port;
       port = dio_port;

    }

     if(addr < DI_addr_start)
     {
         DI_addr_start = addr;
     }

     if(port > DI_count)
     {
       DI_count = port;
     }


}

void modbusDev::doAddDODevice(Json::Value it)
{
    int addr;
    set_int_value(it,&addr,"addr");

    int dio_port;
    set_int_value(it,&dio_port,"port");

    int port;

    port = (addr-DO_addr_start)+dio_port;

    if(DO_addr_start == 0)
    {
       DO_addr_start = addr;
       DO_count = dio_port;
       port = dio_port;

    }

     if(addr < DO_addr_start)
     {
         DO_addr_start = addr;
     }

     if(port > DO_count)
     {
       DO_count = port;
     }

}
int modbusDev::getDevId()
{
    return device_id;
}

bool modbusDev::get_di_data(uint8_t *p_buf,int* p_len)
{
    if(DI_count == 0)
    {
      return false;
    }

    int add;
    add = DI_addr_start;
    if(add)
        add--;

    p_buf[0] = 0x3d;
    p_buf[1] = 0x00;
    p_buf[2] = 0x00;
    p_buf[3] = 0x00;
    p_buf[4] = 0x00;
    p_buf[5] = 0x06;
    p_buf[6] = device_id;
    // p_buf[7] = 0x02;//0x05;
    if (this->useCoilForDi)
    {
      p_buf[7] = 0x01;//0x05;
    }
    else
    {
      p_buf[7] = 0x02;//0x05;
    }
    p_buf[8] = add/256;
    p_buf[9] = add%256;//0x01;
    p_buf[10] = DI_count/256;
    p_buf[11] = DI_count%256;
  
    *p_len = 12;
    
    if (this->appendCrc)
    {
      int crc;
      crc = crc_chk((unsigned char*)p_buf,12);
      p_buf[12]=crc/256;
      p_buf[13]=crc%256;
      *p_len = 14;
    }

    return true;
}

void modbusDev::set_do(int addr,int index,char value,uint8_t*p_buf,int*p_len)
{
   cout<<"modbusDev::set_do "<<addr<<" "<<index<<endl;
   fflush(stdout);
   usleep(10000);
   if(addr) addr--;

   if(index) index--;

   addr = addr+index;

   p_buf[0] = 0x3E;
   p_buf[1] = 0x00;
   p_buf[2] = 0x00;
   p_buf[3] = 0x00;
   p_buf[4] = 0x00;
   p_buf[5] = 0x06;
   p_buf[6] = device_id;
   p_buf[7] = 0x05;
   p_buf[8] = addr/256;
   p_buf[9] = addr%256;//0x01;
   p_buf[10] = 0x00;
   p_buf[11] = 0x00;

   if(value == 1)
   {
       p_buf[10] = (char)0xFF;
       p_buf[11] = 0x00;
   }

  *p_len = 12;
  
  if (this->appendCrc)
  {
    int crc;
    crc = crc_chk((unsigned char*)p_buf,12);
    p_buf[12]=crc/256;
    p_buf[13]=crc%256;
    *p_len = 14;
  }
   //cout<<"modbus_set_do "<<(uint)buf[6]<<" "<<(uint)buf[8]<<" "<<(uint)buf[9]<<" "<<(uint)buf[10]<<" "<<(uint)buf[11]<<endl;
   printf("modbus_set_do %02X %02X %02X %02X %02X %02X",value,(uint8_t)p_buf[6],(uint8_t)p_buf[8],(uint8_t)p_buf[9],(uint8_t)p_buf[10],(uint8_t)p_buf[11]);
   fflush(stdout);
   usleep(10000);
}
int modbusDev::getDiStartAddress()
{
  return this->DI_addr_start;
}
bool modbusDev::set_do(uint8_t*p_buf,int*p_len)
{
  bool ret;

  ret = false;

  if(jobs.size() == 0)
      return false;

  for(vector<dioJob>::iterator iter=jobs.begin(); iter!=jobs.end(); )
  {
       if(iter->m_is_set == false)
       {
           ret = true;
           iter->m_is_set = true;

            set_do(iter->m_addr,iter->m_index,iter->m_value,p_buf,p_len);

            if(iter->m_timeout == 0)
            {
                iter = jobs.erase(iter);

            }
            else
            {
              ++iter;
            }
            break;
        }
        else
        {
          ++iter;
        }

  }

  return ret;
}
bool modbusDev::get_do_data(uint8_t *p_buf,int* p_len)
{
    if(DO_count == 0)
    {
      return false;
    }

    if(set_do(p_buf,p_len))
    {
      cout<<"set do "<<endl;
      fflush(stdout);
      return true;
    }
    int addr;
    addr = DO_addr_start;
    if(addr)
        addr--;

    p_buf[0] = 0x3d;
    p_buf[1] = 0x00;
    p_buf[2] = 0x00;
    p_buf[3] = 0x00;
    p_buf[4] = 0x00;
    p_buf[5] = 0x06;
    p_buf[6] = device_id;
    p_buf[7] = 0x01;//0x05;
    p_buf[8] = addr/256;
    p_buf[9] = addr%256;//0x01;
    p_buf[10] = DO_count/256;
    p_buf[11] = DO_count%256;

    *p_len = 12;
    if (this->appendCrc)
    {
      int crc;
      crc = crc_chk((unsigned char*)p_buf,12);
      p_buf[12]=crc/256;
      p_buf[13]=crc%256;
      *p_len = 14;
    }
    return true;
}

void modbusDev::do_di(uint8_t *p_buf)
{
    //cout<<"do_di"<<endl;
    //cout<<p_buf<<endl;
    for(auto& node : nodes)
    {
      node->do_di(DI_addr_start,p_buf);

    }
}

void modbusDev::do_do(uint8_t *p_buf)
{

  //cout<<"modbusDev::do_do"<<endl;
  for(auto& node : nodes)
  {
    node->do_do(DO_addr_start,p_buf);

  }
}


bool modbusDev::do_action(int parent,int device,int index,int value,int timeout, bool return_last_state)
{
  bool ret;

  ret = false;
  for(auto& node : nodes)
  {
    cout<<"\nmodbusDev::do_action "<<device<<" "<<node->get_did()<<endl;
    fflush(stdout);
    if(node->get_did() == device)
    {
      int last_state = node->getDoValue(index);
      // cout << "get_modbusDev_do_value:" << last_state << ", timeout: " << timeout << endl;;
      // cout << "last___________________________________________________state: " <<last_state<< ", index: " << index <<endl;
      node->setDIOValue(index,0);
      do_doJob(node->get_DO_addr_start(),index,value,timeout,return_last_state,last_state);
      ret = true;

      break;
    }
  }

  return ret;


}
bool modbusDev::cancel_previous_timeout_job(int parent, int device, int index)
{
  bool ret;

  ret = false;

  if(jobs.size() == 0)
      return false;
       
  // cout << "modbusDev:cancel_timeoutjob:" << endl;
  int do_start_addr;
  for(auto& node : nodes)
  {
    if(node->get_did() == device)
    {
      do_start_addr = node->get_DO_addr_start();
      break;
    }
  }
  // for(auto& job : jobs)
  // {
  //   if (job->cancel_timeout_job(do_start_addr, index))
  //   {
  //     jobs.erase(job);
  //     ret = true;      
  //   }
  // }
  for(vector<dioJob>::iterator iter=jobs.begin(); iter!=jobs.end();iter++ )
  {
    if (iter->cancel_timeout_job(do_start_addr, index))
    {
      // iter = jobs.erase(iter);
      ret = true;
      // break;
    }
  }
  for(vector<dioJob>::iterator iter=jobs.begin(); iter!=jobs.end();)
  /*;iter++*/ 
  {
    if (iter->m_cancelled)
    {
      iter = jobs.erase(iter);
    }
    else
    {
      ++iter;
    }
      
  }
  return ret;
}
bool modbusDev::do_action(int parent,int device,int index,int value,int timeout)
{
  bool ret;

  ret = false;
  for(auto& node : nodes)
  {
    if(node->get_did() == device)
    {
      node->setDIOValue(index,0);

      // cout << "do_action_timeout:" << timeout << endl;
      do_doJob(node->get_DO_addr_start(),index,value,timeout);
      ret = true;

      break;
    }
  }

  return ret;


}
void modbusDev::do_doJob(int addr_start,int index,int value,int timeout, bool return_last_state, int last_state)
{
  dioJob job;

  // cout<<__func__<<" "<<addr_start<< ", timeout:" << timeout <<endl;
  fflush(stdout);
  
  job.set(0,0,addr_start,index,value,timeout,return_last_state,last_state);
  jobs.push_back(job);

}
void modbusDev::do_doJob(int addr_start,int index,int value,int timeout)
{
  dioJob job;

  // cout<<__func__<<" "<<addr_start<< ", timeout:" << timeout <<endl;
  fflush(stdout);
  
  job.set(0,0,addr_start,index,value,timeout);
  jobs.push_back(job);

}
void modbusDev::main_timeout()
{
  if(jobs.size() == 0)
      return ;

  for(auto& job : jobs)
  {
    job.do_timeout();

  }
}
