#include "phoneDev.hpp"
#include "wdef.h"
#include "utility.hpp"
#include "baUtil.hpp"
#include <ctime>
#include <chrono>
using namespace std;

phoneDev::phoneDev()
{

    is_need_scan = false;
}

phoneDev::~phoneDev()
{

}

int phoneDev::set(Json::Value it)
{
  //cout<<"\nphoneDev::"<<__func__<<it<<endl;

  set_int_value(it,&m_id,"id");
  set_string_value(it,&name,"name");

  set_string_value(it,&username,"username");
  set_string_value(it,&passwd,"password");
  set_string_value(it,&serverip,"serverip");

  auto it2 = it["Phones"];
  for (auto it3 : it2)
  {
      phoneNode node;
      node.set(it3);

      nodes.push_back(std::move(node));
  }

  return nodes.size();

}

void phoneDev::doPhone()
{
  static int sec = 60;

  if(++sec < 60)
  {
    //sec = 0;
    if(is_need_scan)
    {
      is_need_scan = false;
    }
    else
    {
      return;
    }
  }
  else
  {
    sec = 0;
  }

  //cout<<"\nphoneDev::"<<__func__<<endl;
  Json::Value arr;
  Json::FastWriter writer;

  arr["name"] = username;
  arr["password"] = passwd;

  string json = writer.write(arr);

  stringstream ss;

  string file = "/tmp/POST";

  ss << "wget -T 2 --connect-timeout=60 -O " << file << " --post-data='" << json << "' ";
  ss << "http://"<<serverip<< ":8899/api/account/credentials/verify > /dev/null 2> /dev/null";

  system(ss.str().c_str());

  string str_json = readAllToJson(file);
  update_device_state(str_json);



}
void phoneDev::writeToFile(EDIOState status, ofstream& file)
{
    for(auto& node : nodes)
    {
      node.writeToFile(status, file);
    }
}


bool phoneDev::update(std::string json)
{
  bool ret;

  ret = true;

  Json::Reader reader;
  Json::Value value;
  //std::cout << "Input: " << json << std::endl;
  if (!reader.parse(json, value)) {
    cout << "err update";
    return false;
  }

  string type;
  string caller;
  string callee;
  bool is_need_update_caller = false;
  bool is_need_update_callee = false;
  EDIOState caller_status = EDIOState::RELEASE;
  EDIOState callee_status = EDIOState::RELEASE;

  set_string_value(value,&type,"type");
  set_string_value(value,&caller,"caller");
  set_string_value(value,&callee,"callee");


  if(type == "call_fail")
  {
      is_need_update_caller = true;
      caller_status = EDIOState::RELEASE;
  }
  else if(type == "call_ended")
  {
    is_need_update_caller = true;
    caller_status = EDIOState::RELEASE;
    is_need_update_callee = true;
    callee_status = EDIOState::RELEASE;
  }
  else if(type == "call_established")
  {
      is_need_update_caller = true;
      caller_status = EDIOState::USE;
      is_need_update_callee = true;
      callee_status = EDIOState::USE;
  }
  else if(type == "extension_unregistered" || type == "extension_registered")
  {
    is_need_scan = true;
    return ret;
  }

  if(is_need_update_caller)
  {
    doUpdate(caller,caller_status);
  }
  if(is_need_update_callee)
  {
    doUpdate(callee,callee_status);
  }

  return ret;
}
void phoneDev::doUpdate(string call,EDIOState status)
{
  map_int_string map;

  for(auto& node : nodes)
  {
    if(node.update(call, status,map))
    {
      break;
    }
  }

}
void phoneDev::main_timeout()
{
  for(auto& node : nodes)
  {
    node.main_timeout();

  }

}
bool phoneDev::do_recv()
{

}
void phoneDev::do_state()
{

}

void phoneDev::update_device_state(string json)
{
  Json::Reader reader;
  Json::Value value;

  //cout<<"\nphoneDev::"<<__func__<<endl;
  //std::cout << "Input: " << json << std::endl;
  if (!reader.parse(json, value)) {
    cout <<"\nerror"<<endl;
    return;
  }

  vec_string num_arr;
  for(auto& item : nodes)
  {
      if(item.getInfo() != "")
          num_arr.push_back(item.getInfo());

      if(num_arr.size() >= 100)
      {
          //echo("num 2<br>");
          do_update_device_state(num_arr,value);

          num_arr.erase(num_arr.begin(),num_arr.begin()+num_arr.size());
      }
  }

  if(num_arr.size())
  {
      do_update_device_state(num_arr,value);

  }

}

void phoneDev::do_update_device_state(vec_string num_arr,Json::Value myjson)
{
  if(num_arr.size()==0)
  {
    cout <<"empty(num_arr)";
    return;
  }

  Json::Value arr;
  Json::Value array;
  Json::Value json_id;
  Json::FastWriter writer;

  for(auto& arr : num_arr)
  {
    //json_id[arr.c_str()] = arr;
    array.append(arr);
  }

  arr["extensions"] = array;

  string json = writer.write(arr);

  //cout<<"\nphoneDev::"<<__func__<<" "<<arr<<endl;
  string addr = "http://" + serverip + ":8899/api/extensions/status";

  string file = "/tmp/STATUS1";


  string mytoken;// = myjson["access_token"];

  set_string_value(myjson,&mytoken,"access_token");
  //$cmd = '1234';
  std::stringstream cmd;

  cmd << "curl -v -X POST " << addr;
  cmd << " -H \"Content-Type: application/json\" -H \"access_token: " << mytoken;
  cmd << '"' << " -d '" << json <<"'";
  cmd << " --output " << file << "> /dev/null 2> /dev/null";

  system(cmd.str().c_str());

  json = readAllToJson(file);

  Json::Reader reader;
  Json::Value value;
  //std::cout << "Input: " << json << std::endl;
  if (!reader.parse(json, value)) {
    cout << "err update_device_state";
    return;
  }


  save_device_status(value);


}

void phoneDev::save_device_status(Json::Value json)
{
    auto mystatus = json["extension_status"];

    //cout<<mystatus<<endl;
    std::vector<phoneNode> online_item;
    std::vector<phoneNode> offline_item;

    for(auto item : mystatus)
    {
      string str_status;
      set_string_value(item,&str_status,"status");
      int now_status = conv_string_state(str_status);
      if(now_status == DIO_USE)    continue;

      for(auto& item1 : nodes)
      {
          string str_ext;
          set_string_value(item,&str_ext,"extension_number");
          if(item1.getInfo() == str_ext)
          {
              int old_status = item1.getStatus();
              if(old_status != now_status)
              {
                item1.setStatus(now_status);

                if(now_status == DIO_IDLE)
                {
                  online_item.push_back(item1);
                }
                else if(now_status == DIO_ALARM)
                {
                  offline_item.push_back(item1);

                }
              }

              break;
          }
      }
    }

    sendMessage(offline_item);
    sendMessage(online_item);

}

void phoneDev::sendMessage(std::vector<phoneNode>& items)
{
  string callee = baUtil::get_callee_list();

  if(callee.size() == 0)    return;

  if(items.size() == 0)    return;

  cout<<"\nphoneDev::"<<__func__<<" "<<items.size()<<endl;
  std::vector<phoneNode> mymsg;

  for(auto& item : items)
  {
      mymsg.push_back(item);

      if(mymsg.size() >= 30)
      {
        do_sendMessage(mymsg,callee);
        mymsg.erase (mymsg.begin(),mymsg.begin()+mymsg.size());

      }
  }

  if(mymsg.size())
  {
    do_sendMessage(mymsg,callee);

  }

}

void phoneDev::do_sendMessage(std::vector<phoneNode>& msg,string callee)
{
  if(msg.size() == 0)    return;

  string desc;

  Json::FastWriter writer;
  Json::Value ele;
  Json::Value top;

  for(auto& item : msg)
  {
      desc = item.getNote()+','+desc;
      ele.append(item.getInfo());
  }

  top["number"] = ele;
  if(msg[0].getStatus() == DIO_IDLE)
  {
      desc = desc + " 正常 ";
      top["status"] = "online";
  }
  else
  {
      desc = desc + " 警報 ";
      top["status"] = "offline";
  }

  string json = writer.write(top);
  std::stringstream ss;

  using std::chrono::system_clock;
  std::time_t tt = system_clock::to_time_t (system_clock::now());

  struct std::tm * ptm = std::localtime(&tt);

  ss << desc ;
  ss << ptm->tm_hour<<":"<<ptm->tm_min<<":"<<ptm->tm_sec;

  string in = "desc=" + ss.str() + "&callee="+callee;//input, transfer msg to server side

  baUtil::sendBaSIPMessage(json);
  baUtil::sendSIPMessage(in);
}
