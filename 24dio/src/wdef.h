#ifndef __WDEF_H__
#define __WDEF_H__

#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <string.h>
#include <netdb.h>
#include <sstream>
#include <sys/types.h>
#include <netinet/in.h>
#include <sys/socket.h>
#include <unistd.h>
#include <time.h>

#include <arpa/inet.h>

#include <sys/un.h>
#include <signal.h>
#include <sys/ioctl.h>
#include <pthread.h>
#include <sys/ipc.h>
#include <sys/shm.h>

#include <iostream>
#include <algorithm>
#include <vector>
#include <map>
//using namespace std;
enum class ModbusType { ModbusTCP = 1, ModbusRtuOverTCP =2 };
enum class ModbusFunctionCode { ReadHoldingRegisters = 0x03, ReadInputRegisters = 0x04 };
enum class DataType { Int8 =1 , UInt8 =2 , Int16 =3 ,UInt16 =4 ,Int32 =5,UInt32 =6,Int64= 7,UInt64=8,Float=9,Double=10 };
using vec_string = std::vector<std::string>;
using vec_int = std::vector<int>;
using map_int_string = std::map<int, std::string>;
using map_int_float = std::map<int, float>;

#define MAX_SEND_RETRY 3
#define HELF_HOUR (60*60/2)
#define MAX_UPDATEMESSAGE 100
#define PASS_HOUR 3600
#define MAXDATASIZE 15000

#define MAX_DIO_BUF MAXDATASIZE
static void hexchar(unsigned char c, unsigned char &hex1, unsigned char &hex2)
{
    hex1 = c / 16;
    hex2 = c % 16;
    hex1 += hex1 <= 9 ? '0' : 'a' - 10;
    hex2 += hex2 <= 9 ? '0' : 'a' - 10;
}

static std::string urlencode(std::string s)
{
    const char *str = s.c_str();
    std::vector<char> v(s.size());
    v.clear();
    for (size_t i = 0, l = s.size(); i < l; i++)
    {
        char c = str[i];
        if ((c >= '0' && c <= '9') ||
            (c >= 'a' && c <= 'z') ||
            (c >= 'A' && c <= 'Z') ||
            c == '-' || c == '_' || c == '.' || c == '!' || c == '~' ||
            c == '*' || c == '\'' || c == '(' || c == ')')
        {
            v.push_back(c);
        }
        else if (c == ' ')
        {
            v.push_back('+');
        }
        else
        {
            v.push_back('%');
            unsigned char d1, d2;
            hexchar(c, d1, d2);
            v.push_back(d1);
            v.push_back(d2);
        }
    }

    return std::string(v.cbegin(), v.cend());
}
static std::string UrlEncode(const std::string& value)
{
    static auto hex_digt = "0123456789ABCDEF";
    
    std::string result;
    result.reserve(value.size() << 1);
    
    for (auto ch : value)
    {
        if ((ch >= '0' && ch <= '9')
            || (ch >= 'A' && ch <= 'Z')
            || (ch >= 'a' && ch <= 'z')
            || ch == '-' || ch == '_' || ch == '!'
            || ch == '\'' || ch == '(' || ch == ')'
            || ch == '*' || ch == '~' || ch == '.')  // !'()*-._~
        {
            result.push_back(ch);
        }
        else
        {
            result += std::string("%") +
                      hex_digt[static_cast<unsigned char>(ch) >> 4]
                      +  hex_digt[static_cast<unsigned char>(ch) & 15];
        }
    }
}
static const std::map<uint8_t, std::string> mitsubishiElevatorFloorDisplayNames = {
  { 0x06,"B3"},
  { 0x07, "B2"},
  { 0x17, "B1"},
  { 0x13, "1"},
  { 0x1b, "2"},
  { 0x1a, "3"},
  { 0x18, "4"},
  { 0x19, "5"},
  { 0x1d, "6"},
  { 0x1c, "7"},
  { 0x1e, "8"},
  { 0x1f, "9"},
  { 0x3f, "10"},
  { 0x3e, "11"},
  { 0x3c, "12"},
  { 0x3d, "13"},
  { 0x39, "14"},
  { 0x10, "R"}
};
static const std::map<uint8_t, std::string> fujiElevatorFloorDisplayNames = {
  { 0x00, "N/A"},
  { 0x01, "1F"},
  { 0x02, "2F"},
  { 0x03, "3F"},
  { 0x04, "4F"},
  { 0x05, "5F"},
  { 0x06, "6F"},
  { 0x07, "7F"},
  { 0x08, "8F"},
  { 0x09, "9F"},
  { 0x0a, "10F"},
  { 0x0b, "11F"},
  { 0x0c, "12F"},
  { 0x0d, "13F"},
  { 0x0e, "14F"},
  { 0x0f, "15F"},
  { 0x10, "16F"},
  { 0x11, "17F"},
  { 0x12, "18F"},
  { 0x13, "19F"},
  { 0x14, "20F"},
  // { 0x16, "R"},
  { 0x17, "B1"},
  { 0x18, "B2"},
};
static const std::string liuChuanElevatorFloorDisplayNames[] = {
    "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "NULL", "-1", "-2", "-3", "-4", "-5", "-6", "-7", "-8", "-9", "NULL", "B1", "B2", "B3", "B4", "B5", "B6", "B7", "B8", "B9", "B", "G", "M", "M1", "M2", "M3", "P", "P1", "P2", "P3", "R", "R1", "R2", "R3", "L", "H", "H1", "H2", "H3", "3A", "12A", "12B", "13A", "17A", "17B", "5A", "G1", "G2", "G3", "F", "NULL", "C1", "C2", "C3", "C4", "C", "D1", "D2", "D3", "D4", "D", "1F", "2F", "3F", "4F", "5F", "1C", "2C", "3C", "4C", "NULL", "1B", "2B", "3B", "4B", "1A", "2A", "4A", "CF", "LB", "E", "A", "UB", "LG", "UG", "6A", "6B", "7A", "7B", "5B", "6C", "23A", "24A", "25A", "SB", "15A", "13B", "K", "U", "D", "EG", "KG", "KE1", "KE2", "KE3", "KE4", "KE5", "KE6", "KE7", "KE8", "KE9", "GF", "MZ", "SR", "19A", "Z", "HP", "AB", "PH", "AA", "L1", "L2", "L3", "PB", "-10", "AG", "BE", "RF", "1L", "5L", "1M", "3M", "4M", "B1A", "B2A", "B3A", "B4A", "PM", "14A", "14B", "AS", "15B", "16A", "16B", "22A", "22B", "E1", "E2", "S1", "S2", "S3", "E3", "E4", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64", "P4", "P5", "LD", "JC", "S4", "S5", "SS", "LL", "5C", "9F", "LF", "UF", "FF", "33A", "S6", "S8", "LP", "UP", "MR", "PC", "P6", "P7", "P8", "P9", "P10", "P3A", "P7A", "P8A", "P9A", "AF", "", "", "", "", "", "", "", ""};
static const int  tatungElectronicMeterAddressMappingKwh[] = {
  8242,8244,8246,
  8498,8500,8502,
  8754,8756,8758,
  9010,9012,9014
};
static const int tatungElectronicMeterAddressMappingCurrent[] = {
  8204,8206,8208,
  8460,8462,8464,
  8716,8718,8720,
  8972,8974,8976
};
static const int tatungElectronicMeterAddressMappingPf[] = {
  8266,8268,8270,
  8522,8524,8526,
  8778,8780,8782,
  9034,9036,9038
};
enum {

  NORMAL_RUN=0,
  BA_ROLE,
  RELAY_ROLE,
};


enum DIO_TYPE
{
    DO_TYPE=0,
    DI_TYPE,
};
enum DIOState
{
  DIO_IDLE=1,
  DIO_USE,
  DIO_ALARM,

};

enum class EIPTYPE
{
  Ba=1,
  Center,
};

enum class EDIOState
{
  RELEASE=0,
  USE,
  ALARM,
};

enum class EElecState
{
  NORMAL_STATE=0,
  LOW_STATE,
  MID_STATE,
  HIGH_STATE,

};
enum class EState
{

  INVALID_STATE=-1,
  END_STATE=0,
  WAIT_CONNECT_STATE,
  GET_DATE_TIME_STATE,
  CLR_DO_STATE,
  SET_DO_STATE,
  GET_DI_STATE,
  GET_DO_STATE,
  DONE_CONNECT_STATE,
  GET_RS485_STATE,

};

enum class ECondState
{

  CONDITION_END=0,
  CONDITION_START,
  CONDITION_SECOND,
  CONDITION_SECOND_DURING,
  CONDITION_TIMEOUT,
  CONDITION_ALARM,
  CONDITION_SECOND_TIMEOUT,
  CONDITION_WAIT_ALARM,
  CONDITION_WAIT_ALARM_2,
};

enum {

  TEMP_DEVICE=4,
  ELEC_DEVICE,  
  WATER_METER_DEVICE,
  CO_SENSOR_DEVICE,
  SMART_LAMP_DEVICE,
  CIC_ELEC_DEVICE,
  TATUNG_ELEC_DEVICE,
  YON_GJIA_TEMP_DEVICE,
  BENDER_PEM333_ELEC_DEVICE,
  BENDER_PEM575_ELEC_DEVICE,
  SHIHLIN_ELEC_DEVICE,
  JETEC_WIND_DIRECTION_DEVICE,
  JETEC_RAIN_METER_DEVICE,
  JETEC_SOIL_METER_DEVICE,
  JNC_TEMP_DEVICE,
  PRIMEVOLT_SOLAR_POWER_METER_DEVICE,
  TKD_WATER_METER_DEVICE,
  VMR_MP7_ELEC_DEVICE,
  CO2_DEVICE,
  SHALUN_OPCDA_ELEC_DEVICE,
  WEEMA_ELEC_DEVICE_1P,
  WEEMA_ELEC_DEVICE_3P,
  IRTI_IVA_PERSON_DETECTION_NODE,
  IRTI_IVA_PERSON_COUNTING_NODE,
  WEEMA_IAQ_NODE, // 28
  AEM_DRB_ELEC_DEVICE,
  PINRON_TEMP_DEVICE,
  GENERAL_OPCDA_ELEC_DEVICE,
  GENERAL_OPCDA_WATER_DEVICE,
  SHIHLIN_SPM8_SOLAR_POWER_METER_DEVICE,
  CO_SENSOR_GENERAL_DEVICE,
  GENERAL_AI_DEVICE,
  CIC_BAW1A2A_ELEC_DEVICE,
  GENERAL_SOLAR_DEVICE,
  YON_GJIA_TEMP2_DEVICE,
  YON_GJIA_TEMP_3_IN_1_DEVICE,
  CO_SENSOR_YON_GJIA_DEVICE,
  PM_SENSOR_YON_GJIA_DEVICE,
  YON_GJIA_TEMP_DEVICE_WITH_LUX,//42
  ACUVIM_ELEC_DEVICE,
};
enum {

  MAX_DIO_485_TIMEOUT=60,
  MAX_CONNECT_TIMEOUT=120,
  MAX_DIO_TIMEOUT=60,
  MAX_WAIT_485_TIMEOUT=60,
  MAX_RS485_TIMEOUT=10,
};

enum{
  WEEMA_VENDOR=1,
  ADVANTECH_VENDOR,
  SINEW_VENDOR,
  SOYAL_VENDOR,
  ICP_VENDOR,
  RS485_VENDOR,
  PANASONIC_TWO_LINE_VENDOR,
  LIUCHUAN_ELEVATOR_VENDOR,
  YUNYANG_FIRE_FIGHTING_VENDOR,
  BAOCHUNG_FIRE_FIGHTING_VENDOR,
  MITSUBISHI_ELEVATOR_VENDOR,
  TECH_AIR_CON_VENDOR,
  FUJI_ELEVATOR_VENDOR,
  DOOR_VENDOR=101

};
#endif
