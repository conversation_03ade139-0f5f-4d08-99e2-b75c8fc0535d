#include <ctime>
#include <iomanip>      // std::get_time

#include "utility.hpp"
#include "baUtil.hpp"
#include "client.h"

using namespace std;
#define NEWFILE "/tmp/newfile.txt"

static bool normal_run;
char * getNowTime()
{
  // current date/time based on current system
   time_t now = time(0);

   // convert now to string form
   char* dt = ctime(&now);

   return dt;

}

bool cmp(digitNode &a, digitNode &b) {
    return a.getIndex() < b.getIndex();
    //return false;
}

void set_normal_run()
{
  normal_run = true;
}
void set_float_value(Json::Value it,float* p_float,const char *c_str)
{
  if(it[c_str].isNull())
      *p_float = 0;
  else if(it[c_str].isString() == false)
      *p_float = it[c_str].asInt();
  else
      *p_float = atof(it[c_str].asCString());

}
void set_double_value(Json::Value it,double* p_double,const char *c_str)
{
  if(it[c_str].isNull())
      *p_double = 0;
  else if(it[c_str].isString() == false)
      *p_double = it[c_str].asInt();
  else
      *p_double = atof(it[c_str].asCString());

}

void set_int_value(Json::Value it,int* p_int,const char *c_str)
{
  if(it[c_str].isNull())
      *p_int = 0;
  else if(it[c_str].isString() == false)
      *p_int = it[c_str].asInt();
  else
      *p_int = atoi(it[c_str].asCString());

}

void set_int64_value(Json::Value it,int64_t* p_int64,const char *c_str)
{
  if(it[c_str].isNull())
      *p_int64 = 0;
  else if(it[c_str].isString() == false)
      *p_int64 = it[c_str].asInt64();
  else
      // *p_uint64 = atoi64(it[c_str].asCString());
      *p_int64 = strtoull(it[c_str].asCString(),NULL,10);

}

void set_uint64_value(Json::Value it,uint64_t* p_uint64,const char *c_str)
{
  if(it[c_str].isNull())
      *p_uint64 = 0;
  else if(it[c_str].isString() == false)
      *p_uint64 = it[c_str].asUInt64();
  else
      // *p_uint64 = atoi64(it[c_str].asCString());
      *p_uint64 = strtoull(it[c_str].asCString(),NULL,10);

}

void set_string_value(Json::Value it,std::string *p_string,const char *c_str)
{
  if(it[c_str].isNull())
      p_string->empty();
  else if(it[c_str].isString())
      *p_string = it[c_str].asString();
  else
      p_string->empty();

}
void main_timeout(int timer,bool is_rs485)
{
    static int s_timer = 0;

    s_timer = s_timer + timer;
    if(s_timer > PASS_HOUR)
    {
      s_timer = 0;
      cout << __func__<<" "<<getNowTime();

    }

    baUtil::main_timeout(timer,is_rs485);


}

void process_data(string line)
{
    cout << getNowTime();
    cout << __func__ << " "<<line<<endl;
    fflush(stdout);
    vec_string mypieces =  splitter('=', line.c_str());

    if(mypieces.size() < 2) return;

    if(mypieces[0] == "ACTION")
    {
        std::vector<string> pieces =  splitter(' ', mypieces[1].c_str());
        int pid = std::stoi( pieces[1] );
        int did = std::stoi( pieces[2] );
        int index = std::stoi( pieces[3] );
        int value = std::stoi( pieces[4] );
        int timeout = std::stoi( pieces[5] );

        baUtil::do_action(pid,did,index,value,timeout);

    }

    else if(mypieces[0] == "PHONEALARM")
    {
      std::vector<string> pieces =  splitter(' ', mypieces[1].c_str());
     
      int status = std::stoi( pieces[2] );
     
      baUtil::do_phoneAlarm(pieces[1],status);

    }
    else if(mypieces[0] == "SIP")
    {
      baUtil::doUpdatePhone(mypieces[1]);

    }
    else if(mypieces[0] == "ALARM")
    {
      if(normal_run)
      {
          std::vector<string> pieces =  splitter(' ', mypieces[1].c_str());

          baUtil::clear_alarm(stoi(pieces[1]),stoi(pieces[2]),false);
          baUtil::do_alarm(stoi(pieces[1]),stoi(pieces[2]));
      }
    }
    else if(mypieces[0] == "RELOAD")
    {
      exit_process();
    }


}
std::vector<string> splitter(char c_pattern,const char *p_sentence)
{
  std::vector<string> split_content;

  std::stringstream ss(p_sentence);
  std::string to;

  if (p_sentence != NULL)
  {
    while(std::getline(ss,to,c_pattern)){
      split_content.push_back(to);
    }
  }

return split_content;
}

#include <sstream>
#include <string>

void getPingStatus(ifstream& infile ,EDIOState status,vec_string& ip_string)
{
  std::string line;
  while (std::getline(infile, line))
  {
      std::istringstream iss(line);
      string a, b, c;
      if (!(iss >> a >> b >> c)) { continue; } // error

      if(status == EDIOState::RELEASE)
      {
        if(c == "unreachable")
        {
          ip_string.push_back(a);
        }
      }
      else
      {
        if(c == "alive")
        {
          ip_string.push_back(a);
        }
      }

      // process pair (a,b)
  }
}


void writeIdsToJson(EDIOState ipstatus,map_int_string& ids_map,string& json)
{
   Json::Value array;
   Json::Value json_id;
   Json::Value json_ids;
   Json::Value status;

    Json::FastWriter writer;

    if(ipstatus == EDIOState::RELEASE)
        status["status"] = "1";
    else
        status["status"] = "3";

    for(auto& id_map : ids_map)
    {
      json_id["id"] = id_map.first;
      array.append(json_id);
    }

    status["ids"] = Json::Value(array);

    json = writer.write(status);

}

string getCronString(Json::Value it)
{
    int minute=0;
    int hour=0;
    int day=0;
    int month=0;
    int sun=0;
    int mon=0;
    int tue=0;
    int wed=0;
    int thu=0;
    int fri=0;
    int sat=0;
    int group=0;
    int dir=0;

    set_int_value(it,&minute,"minute");
    set_int_value(it,&hour,"hour");
    set_int_value(it,&day,"day");
    set_int_value(it,&month,"month");
    set_int_value(it,&sun,"sun");
    set_int_value(it,&mon,"mon");
    set_int_value(it,&tue,"tue");
    set_int_value(it,&wed,"wed");
    set_int_value(it,&thu,"thu");
    set_int_value(it,&fri,"fri");
    set_int_value(it,&sat,"sat");
    set_int_value(it,&group,"group");
    set_int_value(it,&dir,"dir");
    stringstream ss;

    if(sun == 0 &&
      mon == 0 &&
      tue == 0 &&
      wed == 0 &&
      thu == 0 &&
      fri == 0 &&
      sat == 0 
       )
       return "";

    ss << minute << " ";

    ss  << hour << " ";
    if(day == 0)
    {
      ss << "* ";
    }
    else
    {
      ss << hour << " ";
    }
    if(month == 0)
    {
      ss <<  "* ";
    }
    else
    {
      ss << month << " ";
    }

    stringstream week;

    if(sun == 1)
    {
      week <<  "0";

    }
    if(mon == 1)
    {
      if(!week.str().empty())
          week << ",";
      week << "1";
    }
    if(tue == 1)
    {
      if(!week.str().empty())
          week << ",";
      week << "2";

    }
    if(wed == 1)
    {
      if(!week.str().empty())
          week << ",";
      week << "3";

    }
    if(thu == 1)
    {
      if(!week.str().empty())
          week << ",";
      week << "4";

    }
    if(fri == 1)
    {
      if(!week.str().empty())
          week << ",";
      week << "5";

    }
    if(sat == 1)
    {
      if(!week.str().empty())
          week << ",";
      week << "6";

    }

   if(week.str().empty())
   {
     week << "*";
   }


   ss << week.str() <<" ";

   ss << "/opt/24dio/timing.sh " << dir <<" " << group << " t" << group << "\n";
   return ss.str();
}

void crontab_to_write(Json::Value it)
{
  ofstream file;      //宣告fstream物件

  file.open(NEWFILE, ios::out | ios::trunc);

  if(!file)    return;

  for(auto it1 : it)
  {
      string str = getCronString(it1);

      file << str;

  }

  file << "58 23 * * * wget http://127.0.0.1/index.php?option=\"com_floor&task=sroots.update_elec_all\" -t3 -o /tmp/update_elec_all -O /tmp/UPDATE_ELEC_ALL \n";

  file.close();

  system("crontab -r");
  string cmd=string("crontab ") + string(NEWFILE);
  system(cmd.c_str());
  cout<<cmd<<endl;
}

bool check_ipaddr(string ipaddr)
{
   stringstream ss;

   ss << "curl https://"<<ipaddr;
   ss << "/index.php?option=\"com_floor&task=sroots.getMyAccount\" -k --connect-timeout 5 -o /tmp/MYACC";

  int ret;
   ret = system(ss.str().c_str());
   //printf("check_online");
   //fflush(stdout);
   return (ret == 0);

}

void copy_file(std::string src,std::string det)
{
  string cmd;

  cmd = "cp "+src+" "+det;
  system(cmd.c_str());
  cout<<cmd<<endl;
}

string readAllToJson(string file)
{
    std::ifstream t(file);
    std::stringstream buffer;
    buffer << t.rdbuf();
    return buffer.str();
    //cout<<buffer.str()<<endl;
}


int conv_string_state(string str_state)
{
  if(str_state == "Registered")
      return DIO_IDLE;
  else if(str_state == "ONCALL")
      return DIO_USE;
  else
      return DIO_ALARM;
}

bool is_valid_time(char *p_data)
{
  struct std::tm tm;
  char date[31];

  snprintf(date,30,"20%s",p_data);
  std::istringstream ss(date);
  ss >> std::get_time(&tm, "%Y'%m/%d %H:%M:%S"); // or just %T in this case

  time_t time1 = mktime(&tm);
  time_t time2 = time(0);

  double dsec = difftime (time1,time2 );

  cout<<"\n"<<ctime(&time2)<<" "<<ctime(&time1)<<" "<<dsec;
  int sec = dsec;
  sec = abs(sec);
  cout<<"\n"<<sec<<endl;
  if(sec < 60)    return true;

  return false;
}

size_t write_data(void *ptr, size_t size, size_t nmemb, FILE *stream) {
    size_t written = fwrite(ptr, size, nmemb, stream);
    return written;
}


void writeToFile(string name,const char *p_error)
{
    fstream file;      //宣告fstream物件

    file.open(name.c_str(), ios::out | ios::trunc);
    //file.open("/tmp/update_ftp", ios::out | ios::trunc);
    if(!file)
    {
         cout<<"\n file not open"<<endl;
    }
    //開啟檔案為輸出狀態，若檔案已存在則清除檔案內容重新寫入

    int size = strlen(p_error);
    file.write(p_error, size);   //將str寫入檔案

    file.close();       //關閉檔案
}
static std::vector<dioJob> jobs;
static bool m_is_empty = true;
void read_jobs_from_file()
{
  string json = readAllToJson("/opt/jobs");
  Json::Reader reader;
  Json::Value value;
  std::cout << "Input: " << json << std::endl;
  if (reader.parse(json, value)) {
      //std::cout << "parsing: " << value ;//<< std::endl;
      // with -std=c++11
      std::cout << "\n---" << std::endl;
      for (auto it : value) {
          cout << "read_jobs_from_file id "<<it["m_device"]<<endl;
          dioJob job;

          set_int_value(it,&job.m_parent,"m_parent");
          set_int_value(it,&job.m_device,"m_device");
          set_int_value(it,&job.m_addr,"m_addr");
          set_int_value(it,&job.m_index,"m_index");
          set_int_value(it,&job.m_value,"m_value");
          set_int_value(it,&job.m_timeout,"m_timeout");
          set_int_value(it,&job.m_last_state,"m_last_state");

          jobs.push_back(job);
      }

  }
}
bool find_add_jobs(int id,dioJob* p_job)
{
   bool ret = false;

   for(auto& job : jobs)
   {
     if(job.m_device == id)
     {
       p_job->m_parent = job.m_parent;
       p_job->m_device = job.m_device;
       p_job->m_addr = job.m_addr;
       p_job->m_index = job.m_index;
       p_job->m_value = job.m_value;
       p_job->m_timeout = job.m_timeout;
       ret = true;
       break;
     }
   }

   return ret;
}
void add_monitor_job(dioJob* job)
{
  jobs.push_back(*job);
}
void save_monitor_job()
{
  Json::Value array;
  Json::Value json_id;
  string json;

  Json::FastWriter writer;

  int cnt = jobs.size();
  if(cnt == 0)
  {
    if(m_is_empty)
    {
      return;
    }
    m_is_empty = true;
  }
  else 
  {
    m_is_empty = false;
  }

  for(auto& job : jobs)
  {
    json_id["m_parent"] = job.m_parent;
    json_id["m_device"] = job.m_device;
    json_id["m_is_set"] = job.m_is_set;
    json_id["m_addr"] = job.m_addr;
    json_id["m_index"] = job.m_index;
    json_id["m_value"] = job.m_value;
    json_id["m_timeout"] = job.m_timeout;
    json_id["m_return_last_state"] = job.m_return_last_state;
    json_id["m_last_state"] = job.m_last_state;
    json_id["m_cancelled"] = job.m_cancelled;

    array.append(json_id);
  }

  clear_jobs();
  json = writer.write(array);
  writeToFile("/opt/jobs",json.c_str());
}

void clear_jobs()
{
  jobs.clear();
}
