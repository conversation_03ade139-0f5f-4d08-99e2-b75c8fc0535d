#include "generalAiNode.hpp"
#include "baUtil.hpp"
#include "utility.hpp"
using namespace std;
template <typename T>
inline std::string ToString(T value) {
    std::stringstream out;
    out << std::fixed;  
    out << value;
    return out.str();
}
generalAiNode::generalAiNode()
{

}

generalAiNode::~generalAiNode()
{
}

int generalAiNode::set(Json::Value it)
{
  analogNode::set(it);

  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");
  set_int_value(it, &status, "status");
  
  set_int_value(it, &m_alarmdir, "alarmdir");
  set_int_value(it, &m_alarm, "alarm");
  set_int_value(it, &alarm_threshold, "alarm_threshold");
  int tmp_int = 0;
  set_int_value(it, &tmp_int, "ga_slave_id");
  slave_id = tmp_int;

  set_int_value(it, &tmp_int, "ga_decimal_digits");
  decimal_digits = tmp_int;

  set_int_value(it, &tmp_int, "ga_update_seconds");
  update_seconds = tmp_int;

  set_int_value(it, &tmp_int, "ga_is_big_endian");
  is_big_endian = tmp_int != 0;

  set_int_value(it, &tmp_int, "ga_is_reversed_word");
  is_reversed_word = tmp_int != 0;

  set_int_value(it, &tmp_int, "ga_modbus_type");
  modbus_type = static_cast<ModbusType>(tmp_int);

  set_int_value(it, &tmp_int, "ga_modbus_function_code");
  modbus_function_code = static_cast<ModbusFunctionCode>(tmp_int);

  set_int_value(it, &tmp_int, "ga_data_type");
  data_type = static_cast<DataType>(tmp_int);
  // if (tmp_int == 9)
  // {
  //   data_type = DataType::Float;
  // }

  set_int_value(it, &tmp_int, "ga_disalarm_percentage");
  disalarm_percentage = tmp_int;

  float tmp_float = 0;
  set_float_value(it, &tmp_float, "ga_scale");
  scale = tmp_float;



  int index = 0;
  set_int_value(it,&index, "header_id");
  header_id = index;
  cout << "set general ai: dbid:" << m_id << "header_id" << header_id << ", index" << index << endl;
  if (this->modbus_type == ModbusType::ModbusTCP)
  {
    uint8_t send[12];
    send[0] = header_id/256;
    send[1] = header_id%256;
    send[2] = 0;
    send[3] = 0;
    send[4] = 0;
    send[5] = 0x06;
    send[6] = slave_id;
    send[7] = (uint8_t)(this->modbus_function_code);
    send[8] = m_addr / 256;
    send[9] = m_addr % 256;
    send[10] = 0x00;
    send[11] = 0x04;


    RS485Msg msg;
    msg.setData(send, 12);
    m_msg.push_back(std::move(msg));  
  }
  else if (this->modbus_type == ModbusType::ModbusRtuOverTCP)
  {
    // uint8_t send[8];    
    // send[0] = offset/256;
    // send[1] = offset%256;
    // send[2] = 0;
    // send[3] = 0x00;
    // send[4] = 0;
    // send[5] = 0x02;
    // int crc;

    // crc = crc_chk(send, 6);
    // send[6] = crc % 256;
    // send[7] = crc / 256;

    // RS485Msg msg;
    // msg.setData(send, 8);
    // m_msg.push_back(std::move(msg));
  }


  iterator = m_msg.begin();

  return 1;
}
void generalAiNode::triggerAlarm()
{
  if (analog_value >= alarm_threshold && status ==3 )
  {
    do_di_release(m_alarmdir, m_alarm);
  }
  else if (analog_value < (alarm_threshold * (disalarm_percentage / 100.0)) && status ==1)
  {
    do_di_event(m_alarmdir, m_alarm);
  }
}
bool generalAiNode::get_di_alarm_triggered()
{
  return status == 3;
  // return m_humidity_alarm_triggered || m_temperature_alarm_triggered;
}
DIOState generalAiNode::get_ediostate()
{
  if (status == 3)
  {
    return DIOState::DIO_ALARM;
  }
  else
  {
    return DIOState::DIO_IDLE;
  }
}
void generalAiNode::do_di_event(int alarmdir, int alarm)
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = alarmdir;
  event.alarm = alarm;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = get_ediostate();

  baUtil::do_di_event(&event);
}

void generalAiNode::do_di_release(int alarmdir, int alarm)
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = alarmdir;
  event.alarm = alarm;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = get_ediostate();

  baUtil::do_di_release(&event);
}
bool generalAiNode::set_data(uint8_t *p_data, int len)
{
  
  if (p_data[0] != (header_id / 256) ||
      p_data[1] != (header_id %256))
  {
    return false;
  }
  // if (m_id == 41516 || m_id == 42711 || m_id == 42704)
  // {

  //  cout << "Gaidebug: " << m_id <<" start set";
  // }
  uint8_t buf[8];
  size_t target_addr = 9;
  bool update_flag = false;
  if (this->modbus_type == ModbusType::ModbusTCP)
  {
    switch (this->data_type)
    {
      // case DataType::Int8:
      //   analog_value = *(int8_t *)&p_data[9];
      //   break;
      // case DataType::UInt8:
      //   analog_value = p_data[9];
      //   break;
      // case DataType::Int16:
      //   buf[0] = p_data[10];
      //   buf[1] = p_data[9];
      //   analog_value = *(int16_t *)&buf[0];
      //   break;
      case DataType::UInt16:
        buf[0] = p_data[10];
        buf[1] = p_data[9];
        analog_value = *(uint16_t *)&buf[0];
        update_flag = true;
        cout << "general ai:" << analog_value << endl;
        break;

      case DataType::Float:
        if (is_reversed_word && !is_big_endian)
        {
          try
          {
            buf[2] = p_data[10];
            buf[3] = p_data[9];
            buf[0] = p_data[12];
            buf[1] = p_data[11];
            analog_value = *(float *)&buf[0];
          }
          catch(const std::exception& e)
          {
            std::cerr << e.what() << '\n';

            cout << "general ai error";
          }
          
      
          update_flag = true;
        }
        break;
    }
    try
    {
      /* code */
      analog_value = round(analog_value * scale * pow(10, decimal_digits)) /pow(10, decimal_digits);    
      analog_value_bytes = *(uint64_t *)&analog_value;
    }
    catch(const std::exception& e)
    {
      // analog_value = 2121.2121;
      std::cerr << e.what() << '\n';

      cout << "general ai calc error";
    }
    // if (m_id == 41516 || m_id == 42711 || m_id == 42704)
    // {

    // cout << "Gaidebug: " << m_id <<" stop set";
    // }
    
  }
  else if (this->modbus_type == ModbusType::ModbusRtuOverTCP)
  {
  }
  if (update_flag)
  {
    updateMessage();
  }
  return true;
}

void generalAiNode::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if (current_timestamp > (last_update_timestamp + update_seconds))
  {
    last_update_timestamp = current_timestamp;
    triggerAlarm();
    sendMessageToBa();
  }
}
void generalAiNode::sendMessageToBa()
{
  static int s_cnt = MAX_UPDATEMESSAGE;
  bool is_show;

  is_show = false;
  if (++s_cnt > MAX_UPDATEMESSAGE)
  {
    s_cnt = 0;
    is_show = true;
  }
  std::stringstream ss;
  ss << "/index.php?option=\"com_floor&task=sroots.update_analog" << "&id=" << m_id <<"&use_uint=0&text_value="<< urlencode(ToString(analog_value)) << "__" << analog_value_bytes <<"&decimal_value=" << analog_value<< "\" ";

  WSendMsg msg;

  msg.set(ss.str(), "/tmp/sendrs485analogmsg", "/tmp/SENDRS485ANALOGMSG", true, true);
  baUtil::add_sendMessage(&msg);
}