# 警報音效播放失效問題修復記錄

**修改時間：** 2025年1月23日  
**問題描述：** 警報音效有時無法播放，重新整理頁面後恢復正常  
**修改人員：** Augment Agent  

## 問題分析

重新整理頁面就能解決，表示問題出在：
1. `current_time` 拼寫錯誤（應為 `currentTime`）
2. `load()` 後立即 `play()` 可能還沒載入完成

## 修復方案

**檔案位置：** `web/com_floor-1.0.0/site/views/sroots/tmpl/default.php`

### 修改前
```javascript
if(is_alarm == 1)
{
    var path = '/images/alarm_audios/' + obj.alarm_sound_file;
    if (myalarm_source.attr('src') != path)
    {
        myalarm_source.attr('src', path);
        myalarm[0].pause();
        myalarm[0].current_time =0;  // 錯誤：應為 currentTime
        myalarm[0].load();
        myalarm[0].play();  // 問題：load() 後立即 play()
    }
}
```

### 修改後
```javascript
if(is_alarm == 1)
{
    var path = '/images/alarm_audios/' + obj.alarm_sound_file;
    if (myalarm_source.attr('src') != path)
    {
        myalarm_source.attr('src', path);
        myalarm[0].pause();
        myalarm[0].currentTime = 0;  // 修正：current_time -> currentTime
        myalarm[0].load();
        
        // 修正：等待載入完成後再播放
        myalarm[0].addEventListener('canplaythrough', function() {
            myalarm[0].play();
        }, { once: true });
    }
}
```

## 修改說明

1. **修正屬性名稱**：`current_time` → `currentTime`
2. **等待載入完成**：使用 `canplaythrough` 事件確保音效載入完成後再播放
3. **一次性事件**：使用 `{ once: true }` 避免重複綁定

這是最小化的修改，只針對明確的問題點進行修正。

---

**修改完成時間：** 2025年1月23日 17:00
